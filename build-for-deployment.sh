#!/bin/bash

# Build ERP POS System for Deployment
echo "🚀 Building ERP POS System for deployment..."

# Build React POS App
echo "📱 Building React POS App..."
cd erp-pos-app
npm install
npm run build

# Deploy React app to Laravel public directory
echo "📁 Deploying React app to Laravel..."
mkdir -p ../erp-pos-system/public/pos
cp -r dist/* ../erp-pos-system/public/pos/
sed -i 's|/assets/|/pos/assets/|g' ../erp-pos-system/public/pos/index.html

cd ..

# Prepare <PERSON>vel backend
echo "🔧 Preparing <PERSON><PERSON> backend..."
cd erp-pos-system

# Install PHP dependencies
composer install --no-dev --optimize-autoloader

# Clear and cache configurations
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear

echo "✅ Build completed successfully!"
echo "🌐 Ready for deployment to Render.com"
echo ""
echo "📋 Next steps:"
echo "1. Commit all changes to Git"
echo "2. Push to your repository"
echo "3. Deploy to Render.com using render.yaml"
echo ""
echo "🔗 Your app will be available at:"
echo "   - Backend: https://erp-pos-system.onrender.com"
echo "   - POS App: https://erp-pos-system.onrender.com/pos"
