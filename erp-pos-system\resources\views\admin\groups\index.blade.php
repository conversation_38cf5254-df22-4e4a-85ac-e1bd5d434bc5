@extends('layouts.admin')

@section('title', 'Groups Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Groups Management</h3>
                    <a href="{{ route('admin.groups.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add New Group
                    </a>
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <select class="form-control" id="divisionFilter">
                                <option value="">All Divisions</option>
                                @foreach($divisions as $division)
                                    <option value="{{ $division->id }}">{{ $division->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-4">
                            <select class="form-control" id="statusFilter">
                                <option value="">All Status</option>
                                <option value="1">Active</option>
                                <option value="0">Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <input type="text" class="form-control" id="searchInput" placeholder="Search groups...">
                        </div>
                    </div>

                    @if($groups->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped" id="groupsTable">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Division</th>
                                        <th>Category</th>
                                        <th>Products</th>
                                        <th>Status</th>
                                        <th>Sort Order</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($groups as $group)
                                        <tr>
                                            <td>
                                                @if($group->icon)
                                                    <i class="{{ $group->icon }}" style="color: {{ $group->color ?? '#007bff' }}"></i>
                                                @endif
                                                {{ $group->name }}
                                            </td>
                                            <td>{{ $group->division->name }}</td>
                                            <td>{{ $group->division->category->name }}</td>
                                            <td>
                                                <span class="badge badge-info">{{ $group->products->count() }}</span>
                                            </td>
                                            <td>
                                                @if($group->is_active)
                                                    <span class="badge badge-success">Active</span>
                                                @else
                                                    <span class="badge badge-danger">Inactive</span>
                                                @endif
                                            </td>
                                            <td>{{ $group->sort_order ?? 0 }}</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.groups.show', $group) }}" class="btn btn-sm btn-info">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.groups.edit', $group) }}" class="btn btn-sm btn-warning">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('admin.groups.destroy', $group) }}" method="POST" class="d-inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-danger" 
                                                                onclick="return confirm('Are you sure you want to delete this group?')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $groups->links() }}
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Groups Found</h5>
                            <p class="text-muted">Start by creating your first group.</p>
                            <a href="{{ route('admin.groups.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create First Group
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Search functionality
    $('#searchInput').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#groupsTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
        });
    });

    // Division filter
    $('#divisionFilter').on('change', function() {
        var divisionId = $(this).val();
        $('#groupsTable tbody tr').each(function() {
            if (divisionId === '' || $(this).data('division-id') == divisionId) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // Status filter
    $('#statusFilter').on('change', function() {
        var status = $(this).val();
        $('#groupsTable tbody tr').each(function() {
            if (status === '' || $(this).data('status') == status) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });
});
</script>
@endpush
@endsection
