// syncService.cjs - Electron main process sync logic
const axios = require('axios');
const { db } = require('../db/sqlite.cjs');
const path = require('path');

// Set your local Laravel backend API base URL here
const ERP_API_BASE = 'http://127.0.0.1:8000/api';
let API_TOKEN = null;

// Login to get API token
async function login(username, password) {
  try {
    const response = await axios.post(`${ERP_API_BASE}/auth/login`, {
      username,
      password
    });
    API_TOKEN = response.data.token;
    return { success: true, token: API_TOKEN };
  } catch (err) {
    return { success: false, error: err.message };
  }
}

// Configure axios with auth token
function getAuthHeaders() {
  return API_TOKEN ? {
    'Authorization': `Bearer ${API_TOKEN}`,
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  } : {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  };
}

// Sync inventory from Laravel API to local SQLite
async function syncInventoryFromERP() {
  try {
    const response = await axios.get(`${ERP_API_BASE}/products`, {
      headers: getAuthHeaders()
    });
    
    // Get products from response
    const products = response.data.data;
    
    // Clear local table before sync
    db.prepare('DELETE FROM products').run();
    
    // Insert products into local database
    const insert = db.prepare('INSERT INTO products (id, name, sku, price, stock) VALUES (?, ?, ?, ?, ?)');
    
    for (const item of products) {
      insert.run(
        item.id, 
        item.name, 
        item.sku || '', 
        item.price, 
        item.stock_quantity || 0
      );
    }
    
    return { success: true, count: products.length };
  } catch (err) {
    return { success: false, error: err.message };
  }
}

// Push unsynced sales to Laravel API
async function syncSalesToERP() {
  try {
    // Get unsynced sales from local database
    const unsyncedSales = db.prepare('SELECT * FROM sales WHERE synced = 0').all();
    
    if (unsyncedSales.length === 0) {
      return { success: true, count: 0, message: 'No unsynced sales found' };
    }
    
    // Prepare sales data for API
    const salesData = [];
    
    for (const sale of unsyncedSales) {
      // Get items for this sale
      const items = db.prepare('SELECT * FROM sale_items WHERE sale_id = ?').all(sale.id);
      
      // Get payments for this sale
      const payments = db.prepare('SELECT * FROM payments WHERE sale_id = ?').all(sale.id);
      
      salesData.push({
        local_id: sale.id.toString(),
        branch_id: sale.branch_id,
        tax_amount: sale.tax_amount,
        discount_amount: sale.discount_amount,
        sale_date: sale.created_at,
        notes: sale.notes || '',
        items: items.map(item => ({
          product_id: item.product_id,
          quantity: item.quantity,
          unit_price: item.price,
          discount_amount: item.discount_amount || 0
        })),
        payments: payments.map(payment => ({
          method: payment.method,
          amount: payment.amount
        }))
      });
    }
    
    // Send sales data to API
    const response = await axios.post(`${ERP_API_BASE}/sales/sync`, {
      pos_device_id: db.prepare('SELECT value FROM config WHERE key = "pos_device_id"').get().value,
      sales: salesData
    }, {
      headers: getAuthHeaders()
    });
    
    // Update synced status in local database
    const updateSynced = db.prepare('UPDATE sales SET synced = 1 WHERE id = ?');
    
    let syncedCount = 0;
    for (const result of response.data.data.results) {
      if (result.status === 'success') {
        updateSynced.run(result.local_id);
        syncedCount++;
      }
    }
    
    return {
      success: true,
      count: syncedCount,
      total: salesData.length,
      results: response.data.data.results
    };
  } catch (err) {
    return { success: false, error: err.message };
  }
}

// Test connection to Laravel backend
async function testERPConnection() {
  try {
    // Try to get the first product as a test
    const response = await axios.get(`${ERP_API_BASE}/products?per_page=1`, {
      headers: getAuthHeaders()
    });
    
    return {
      connected: true,
      data: {
        message: 'Successfully connected to ERP system',
        products_available: response.data.data.total || 0
      }
    };
  } catch (err) {
    return { connected: false, error: err.message };
  }
}

module.exports = {
  login,
  syncInventoryFromERP,
  syncSalesToERP,
  testERPConnection
};
