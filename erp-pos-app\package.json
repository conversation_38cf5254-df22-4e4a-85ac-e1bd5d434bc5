{"name": "erp-pos-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "electron": "electron ./electron/main.cjs", "dev:electron": "set VITE_DEV_SERVER_URL=http://localhost:5173 && electron ./electron/main.cjs"}, "dependencies": {"@types/react-router-dom": "^5.3.3", "axios": "^1.11.0", "better-sqlite3": "^12.2.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.8.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tailwindcss/postcss": "^4.1.11", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "electron": "^37.2.4", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}