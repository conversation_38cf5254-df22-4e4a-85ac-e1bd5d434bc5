/* ActivationPage Component Styles */

.activation-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #312e81 0%, #1e3a8a 50%, #581c87 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
}

.activation-wrapper {
  width: 100%;
  max-width: 448px;
}

.activation-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(24px);
  border-radius: 24px;
  padding: 32px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Header Styles */
.activation-header {
  text-align: center;
  margin-bottom: 32px;
}

.activation-icon-container {
  display: inline-block;
  padding: 16px;
  background: linear-gradient(135deg, #6366f1 0%, #3b82f6 100%);
  border-radius: 16px;
  margin-bottom: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.activation-icon {
  width: 48px;
  height: 48px;
  background: white;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.activation-title {
  font-size: 30px;
  font-weight: bold;
  color: white;
  margin-bottom: 4px;
  margin-top: 0;
}

.activation-subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  margin: 0;
}

/* Form Styles */
.activation-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.activation-form-group {
  display: flex;
  flex-direction: column;
}

.activation-label {
  display: block;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.activation-input {
  width: 100%;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: white;
  font-size: 14px;
  transition: all 0.2s ease;
  backdrop-filter: blur(8px);
  box-sizing: border-box;
}

.activation-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.activation-input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.activation-select {
  width: 100%;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: white;
  font-size: 14px;
  transition: all 0.2s ease;
  backdrop-filter: blur(8px);
  box-sizing: border-box;
  cursor: pointer;
}

.activation-select:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.activation-select option {
  background: #1f2937;
  color: white;
}

/* Button Styles */
.activation-button {
  width: 100%;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.activation-button-primary {
  background: linear-gradient(135deg, #6366f1 0%, #3b82f6 100%);
  color: white;
}

.activation-button-primary:hover {
  background: linear-gradient(135deg, #5b21b6 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.activation-button-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.activation-button-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
}

.activation-button-disabled {
  background: rgba(107, 114, 128, 0.5);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
}

.activation-button-disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Loading Spinner */
.activation-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error Message */
.activation-error {
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 12px;
  padding: 12px;
  font-size: 14px;
  color: #fecaca;
}

/* Success Message */
.activation-success {
  background: rgba(34, 197, 94, 0.2);
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 12px;
  padding: 12px;
  font-size: 14px;
  color: #bbf7d0;
}

/* Company Info Display */
.activation-company-info {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  margin-top: 16px;
}

.activation-company-name {
  color: white;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 8px;
}

.activation-company-details {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  line-height: 1.5;
}

.activation-company-status {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  margin-top: 8px;
}

.activation-company-status-active {
  background: rgba(34, 197, 94, 0.2);
  color: #bbf7d0;
}

.activation-company-status-expired {
  background: rgba(239, 68, 68, 0.2);
  color: #fecaca;
}

/* Step Navigation */
.activation-steps {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 24px;
}

.activation-step {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: all 0.2s ease;
}

.activation-step-active {
  background: #6366f1;
  transform: scale(1.2);
}

/* Input Group */
.activation-input-group {
  display: flex;
  gap: 12px;
}

.activation-input-group .activation-input {
  flex: 1;
}

.activation-input-group .activation-button {
  width: auto;
  padding: 12px 16px;
  white-space: nowrap;
}

/* Responsive Design */
@media (max-width: 480px) {
  .activation-container {
    padding: 16px;
  }

  .activation-card {
    padding: 24px;
  }

  .activation-title {
    font-size: 24px;
  }

  .activation-icon-container {
    padding: 12px;
  }

  .activation-icon {
    width: 36px;
    height: 36px;
  }

  .activation-input-group {
    flex-direction: column;
  }
}

/* Animation for card entrance */
.activation-card {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
