@extends('layouts.admin')

@section('title', 'Companies Management - ERP System')

@section('page-title', 'Companies Management')

@section('breadcrumb', 'Home > Admin > Companies')

@section('styles')
<style>
    .table-container {
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        overflow: hidden;
        margin-bottom: 20px;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    table { width: 100%; border-collapse: collapse; min-width: 800px; }
    th, td { padding: 15px; text-align: left; border-bottom: 1px solid #f1f3f4; }
    th { background: #f8f9fa; font-weight: 600; color: #2c3e50; font-size: 13px; text-transform: uppercase; letter-spacing: 0.5px; }
    tr:hover { background: #f8f9fa; }
    .status-active { color: #27ae60; font-weight: 600; }
    .status-expired { color: #e74c3c; font-weight: 600; }
    .actions { display: flex; gap: 8px; flex-wrap: wrap; }
    .search-box { margin-bottom: 25px; }
    .search-box input {
        padding: 12px 16px;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        width: 100%;
        max-width: 350px;
        font-size: 14px;
        transition: all 0.3s ease;
    }
    .search-box input:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }
    .header-actions {
        display: flex;
        gap: 15px;
        margin-bottom: 25px;
        align-items: center;
        flex-wrap: wrap;
    }
    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    .stat-card {
        background: white;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        text-align: center;
        transition: transform 0.3s ease;
    }
    .stat-card:hover {
        transform: translateY(-5px);
    }
    .stat-number {
        font-size: 28px;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 5px;
    }
    .stat-label {
        font-size: 13px;
        color: #7f8c8d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Mobile Card View */
    .mobile-card-view {
        display: none;
    }

    /* Mobile Responsive Styles */
    @media (max-width: 768px) {
        .search-box input {
            max-width: none;
        }

        .header-actions {
            flex-direction: column;
            align-items: stretch;
        }

        .header-actions .btn {
            width: 100%;
            text-align: center;
            margin-bottom: 10px;
        }

        .stats-cards {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .actions {
            flex-direction: column;
            gap: 5px;
            min-width: 80px;
        }

        .actions .btn {
            padding: 5px 8px !important;
            font-size: 11px !important;
            width: 100%;
            text-align: center;
        }

        th, td {
            padding: 8px 6px;
            font-size: 12px;
        }
    }

    @media (max-width: 480px) {
        .table-container table {
            display: none;
        }

        .mobile-card-view {
            display: block;
        }

        .company-mobile-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }

        .company-mobile-header {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .company-mobile-info {
            font-size: 13px;
            color: #666;
            line-height: 1.5;
            margin-bottom: 10px;
        }

        .company-mobile-info div {
            margin-bottom: 5px;
        }

        .company-mobile-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .company-mobile-actions .btn {
            flex: 1;
            min-width: 70px;
            padding: 8px 10px !important;
            font-size: 11px !important;
            text-align: center;
        }

        .stat-card {
            padding: 15px 10px;
        }

        .stat-number {
            font-size: 24px;
        }

        .stat-label {
            font-size: 12px;
        }
    }
</style>
@endsection

@section('content')
<div class="header-actions">
    <a href="{{ route('admin.companies.create') }}" class="btn btn-primary">
        <i style="margin-right: 8px;">➕</i> Add New Company
    </a>
</div>

<!-- Stats Cards -->
<div class="stats-cards">
    <div class="stat-card">
        <div class="stat-number">{{ $companies->total() }}</div>
        <div class="stat-label">Total Companies</div>
    </div>
    <div class="stat-card">
        <div class="stat-number">{{ $companies->where('license_expiry', '>', now())->count() }}</div>
        <div class="stat-label">Active Licenses</div>
    </div>
    <div class="stat-card">
        <div class="stat-number">{{ $companies->where('license_expiry', '<', now())->count() }}</div>
        <div class="stat-label">Expired Licenses</div>
    </div>
</div>

<div class="search-box">
    <input type="text" id="searchInput" placeholder="🔍 Search companies..." onkeyup="searchTable()">
</div>

<div class="mobile-scroll-hint">
    👈 Scroll horizontally to see all columns
</div>

<div class="table-container table-responsive">
            <table id="companiesTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Company Name</th>
                        <th>License Key</th>
                        <th>License Status</th>
                        <th>License Expiry</th>
                        <th>POS Limit</th>
                        <th>Active POS</th>
                        <th>Brands</th>
                        <th>Users</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($companies as $company)
                        <tr>
                            <td>{{ $company->id }}</td>
                            <td><strong>{{ $company->name }}</strong></td>
                            <td><code>{{ $company->license_key }}</code></td>
                            <td>
                                @if($company->license_expiry > now())
                                    <span class="status-active">Active</span>
                                @else
                                    <span class="status-expired">Expired</span>
                                @endif
                            </td>
                            <td>{{ $company->license_expiry->format('M d, Y') }}</td>
                            <td>{{ $company->pos_limit }}</td>
                            <td>
                                @php
                                    $activePOS = $company->branches->sum(function($branch) { 
                                        return $branch->posDevices->where('status', 'active')->count(); 
                                    });
                                @endphp
                                {{ $activePOS }}
                            </td>
                            <td>{{ $company->brands->count() }}</td>
                            <td>{{ $company->users->count() }}</td>
                            <td>
                                <div class="actions">
                                    <a href="{{ route('admin.companies.show', $company) }}" class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">View</a>
                                    <a href="{{ route('admin.companies.edit', $company) }}" class="btn btn-warning" style="padding: 5px 10px; font-size: 12px;">Edit</a>
                                    <form method="POST" action="{{ route('admin.companies.destroy', $company) }}" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this company? This will delete all related data.')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">Delete</button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="10" style="text-align: center; color: #666; padding: 40px;">
                                No companies found. <a href="{{ route('admin.companies.create') }}">Add the first company</a>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Mobile Card View -->
        <div class="mobile-card-view">
            @forelse($companies as $company)
                <div class="company-mobile-card">
                    <div class="company-mobile-header">
                        {{ $company->name }}
                    </div>
                    <div class="company-mobile-info">
                        <div><strong>ID:</strong> {{ $company->id }}</div>
                        <div><strong>License:</strong> <code>{{ $company->license_key }}</code></div>
                        <div><strong>Status:</strong>
                            @if($company->license_expiry > now())
                                <span class="status-active">Active</span>
                            @else
                                <span class="status-expired">Expired</span>
                            @endif
                        </div>
                        <div><strong>Expiry:</strong> {{ $company->license_expiry->format('M d, Y') }}</div>
                        <div><strong>POS Limit:</strong> {{ $company->pos_limit }}</div>
                        <div><strong>Active POS:</strong>
                            @php
                                $activePosCount = $company->posDevices()->where('status', 'active')->count();
                            @endphp
                            {{ $activePosCount }}
                        </div>
                        <div><strong>Brands:</strong> {{ $company->brands->count() }}</div>
                        <div><strong>Users:</strong> {{ $company->users->count() }}</div>
                    </div>
                    <div class="company-mobile-actions">
                        <a href="{{ route('admin.companies.show', $company) }}" class="btn btn-primary">View</a>
                        <a href="{{ route('admin.companies.edit', $company) }}" class="btn btn-warning">Edit</a>
                        <form method="POST" action="{{ route('admin.companies.destroy', $company) }}" style="display: inline; flex: 1;" onsubmit="return confirm('Are you sure you want to delete this company? This will delete all related data.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger" style="width: 100%;">Delete</button>
                        </form>
                    </div>
                </div>
            @empty
                <div class="company-mobile-card">
                    <div class="company-mobile-header">No Companies Found</div>
                    <div class="company-mobile-info">
                        <a href="{{ route('admin.companies.create') }}">Add the first company</a>
                    </div>
                </div>
            @endforelse
        </div>

@if($companies->hasPages())
    <div style="margin-top: 30px; text-align: center;">
        {{ $companies->links() }}
    </div>
@endif
@endsection

@section('scripts')
<script>
    function searchTable() {
        const input = document.getElementById('searchInput');
        const filter = input.value.toLowerCase();
        const table = document.getElementById('companiesTable');
        const rows = table.getElementsByTagName('tr');

        for (let i = 1; i < rows.length; i++) {
            const cells = rows[i].getElementsByTagName('td');
            let found = false;

            for (let j = 0; j < cells.length - 1; j++) {
                if (cells[j].textContent.toLowerCase().includes(filter)) {
                    found = true;
                    break;
                }
            }

            rows[i].style.display = found ? '' : 'none';
        }
    }
</script>
@endsection
