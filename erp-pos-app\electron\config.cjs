// config.cjs - ERP sync configuration
module.exports = {
  // Laravel API connection settings
  api: {
    baseUrl: 'http://127.0.0.1:8000/api',
    timeout: 30000, // 30 seconds
    retryAttempts: 3
  },
  
  // Local database settings
  database: {
    // How often to check for pending sales to sync (in milliseconds)
    syncInterval: 300000, // 5 minutes
    
    // Maximum number of records to sync in one batch
    batchSize: 50
  },
  
  // Authentication settings
  auth: {
    // How long to keep the token valid before requiring re-login (in milliseconds)
    tokenExpiry: 86400000 // 24 hours
  },
  
  // Logging settings
  logging: {
    // Log levels: 'error', 'warn', 'info', 'debug'
    level: 'info',
    
    // Maximum log file size (in bytes)
    maxSize: 5242880, // 5MB
    
    // Maximum number of log files to keep
    maxFiles: 5
  }
};
