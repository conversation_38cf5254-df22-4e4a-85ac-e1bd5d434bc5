/**
 * React Hook for managing POS device activation with MAC address binding
 */

import { useState, useEffect, useCallback } from 'react';
import { macDeviceService } from '../services/macDeviceService';
import type { MacActivationStatus } from '../services/macDeviceService';

export interface UseMacDeviceActivationReturn {
  isActivated: boolean;
  isLoading: boolean;
  deviceInfo: MacActivationStatus | null;
  error: string | null;
  macAddress: string | null;
  activateDevice: (companyId: string, activationKey: string) => Promise<boolean>;
  deactivateDevice: (reason?: string) => Promise<boolean>;
  checkStatus: () => Promise<void>;
  refreshStatus: () => Promise<void>;
}

export const useMacDeviceActivation = (): UseMacDeviceActivationReturn => {
  const [isActivated, setIsActivated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [deviceInfo, setDeviceInfo] = useState<MacActivationStatus | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [macAddress, setMacAddress] = useState<string | null>(null);

  /**
   * Check device activation status with MAC address
   */
  const checkStatus = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const status = await macDeviceService.initialize();
      
      setIsActivated(status.activated);
      setDeviceInfo(status);
      setMacAddress(macDeviceService.getMacAddressValue());
      
      if (!status.activated && status.message) {
        setError(status.message);
      }
      
      // Start heartbeat if activated
      if (status.activated) {
        // You can add heartbeat functionality here if needed
        console.log('Device activated with MAC:', macDeviceService.getMacAddressValue());
      }
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      setIsActivated(false);
      setDeviceInfo(null);
      setMacAddress(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Refresh activation status
   */
  const refreshStatus = useCallback(async () => {
    await checkStatus();
  }, [checkStatus]);

  /**
   * Activate device with company ID and activation key
   */
  const activateDevice = useCallback(async (companyId: string, activationKey: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);
      
      const result = await macDeviceService.activateDevice(companyId, activationKey);
      
      if (result.success) {
        // Refresh status after successful activation
        await checkStatus();
        return true;
      } else {
        setError(result.message);
        return false;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Activation failed';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [checkStatus]);

  /**
   * Deactivate device
   */
  const deactivateDevice = useCallback(async (_reason: string = 'User requested'): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);
      
      // For now, just clear local storage since we don't have a deactivate endpoint
      localStorage.removeItem('pos_device_fingerprint');
      localStorage.removeItem('pos_device_mac_address');
      localStorage.removeItem('erp_server_url');
      localStorage.removeItem('company_data');
      localStorage.removeItem('app_activated');
      
      setIsActivated(false);
      setDeviceInfo(null);
      setMacAddress(null);
      return true;
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Deactivation failed';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Initialize on mount
   */
  useEffect(() => {
    checkStatus();
  }, [checkStatus]);

  /**
   * Auto-refresh status every 5 minutes
   */
  useEffect(() => {
    const interval = setInterval(() => {
      if (isActivated) {
        refreshStatus();
      }
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [isActivated, refreshStatus]);

  return {
    isActivated,
    isLoading,
    deviceInfo,
    error,
    macAddress,
    activateDevice,
    deactivateDevice,
    checkStatus,
    refreshStatus
  };
};
