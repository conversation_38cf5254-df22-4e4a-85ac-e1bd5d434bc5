# POS System - Transaction Visibility Guide

## Overview
This document explains how the Point of Sale (POS) system works and how to view transactions in both the POS interface and the admin cloud interface.

## System Architecture

### 1. POS Interface
- **Location**: `/admin/sales/pos`
- **Purpose**: Real-time sales processing for cashiers
- **Features**:
  - Product browsing and search
  - Cart management
  - Payment processing
  - Receipt generation

### 2. Admin Sales Management
- **Location**: `/admin/sales`
- **Purpose**: View and manage all sales transactions
- **Features**:
  - Sales dashboard with totals
  - Filtering by date, status, branch
  - Detailed transaction views
  - Export capabilities

## How Transactions Flow

### Step 1: POS Sale Creation
1. Cashier adds products to cart in POS interface
2. Customer completes payment
3. System calls `POST /api/sales` endpoint
4. SaleController processes the transaction

### Step 2: Database Storage
- Sale record created in `sales` table
- Sale items stored in `sale_items` table
- Payment record created in `payments` table
- Product stock levels updated

### Step 3: Visibility in Admin Interface
- Sales appear immediately in admin dashboard
- Filterable by various criteria
- Accessible to authorized users only

## Key Files and Components

### Backend (<PERSON>vel)
- `app/Http/Controllers/SaleController.php` - Main sales processing logic
- `routes/api.php` - API endpoints for sales
- `database/migrations/` - Database schema for sales

### Frontend (Blade Templates)
- `resources/views/admin/sales/pos.blade.php` - POS interface
- `resources/views/admin/sales/index.blade.php` - Sales management

## Testing the System

### 1. Test POS Sale
```bash
# Run the test script to verify functionality
php test_pos_sales.php
```

### 2. Manual Testing
1. Open POS interface: `/admin/sales/pos`
2. Process a test sale
3. Check admin interface: `/admin/sales`
4. Verify the sale appears in the list

## Common Issues and Solutions

### Issue: Sales not appearing in admin interface
- **Solution**: Check database connection and ensure SaleController is working
- **Check**: Run the test script to verify connectivity

### Issue: Permission errors
- **Solution**: Ensure user has proper permissions to view sales
- **Check**: User role and company/branch assignments

### Issue: Stock not updating
- **Solution**: Verify product stock tracking is enabled
- **Check**: Product model and stock_quantity field

## API Endpoints

### Create POS Sale
- **Endpoint**: `POST /api/sales`
- **Purpose**: Process real-time sales from POS
- **Authentication**: Required (Sanctum token)

### Get Sales List
- **Endpoint**: `GET /api/sales`
- **Purpose**: Retrieve sales for admin interface
- **Filters**: date_from, date_to, status, branch_id

## Security Considerations

- Sales are filtered by user permissions
- Master admin sees all sales
- Regular users see only their company's sales
- Branch managers see only their branch's sales

## Monitoring and Logging

- All sales are logged with timestamps
- Payment methods are recorded
- Stock changes are tracked
- Error handling with proper rollback on failures

## Support
For issues with sales visibility, check:
1. Database connectivity
2. User permissions
3. API endpoint accessibility
4. Error logs in storage/logs/
