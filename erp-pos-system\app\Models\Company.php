<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Company extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'license_key',
        'license_expiry',
        'pos_limit',
        'status',
        'contact_email',
        'contact_phone',
        'address',
        'notes',
        'max_devices',
        'active_devices',
        'allow_device_transfer',
        'device_restrictions',
        'activated_mac_addresses'
    ];

    protected $casts = [
        'license_expiry' => 'date',
        'allow_device_transfer' => 'boolean',
        'device_restrictions' => 'array',
        'activated_mac_addresses' => 'array',
    ];

    public function brands()
    {
        return $this->hasMany(Brand::class);
    }

    public function branches()
    {
        return $this->hasMany(Branch::class);
    }

    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function posLimits()
    {
        return $this->hasMany(PosLimit::class);
    }

    public function posDevices()
    {
        return $this->hasManyThrough(PosDevice::class, Branch::class);
    }

    /**
     * Get active POS devices for this company
     */
    public function activePosDevices()
    {
        return $this->posDevices()->where('is_activated', true);
    }

    /**
     * Check if company can activate more devices
     */
    public function canActivateMoreDevices()
    {
        return $this->active_devices < $this->max_devices;
    }

    /**
     * Get remaining device slots
     */
    public function getRemainingDeviceSlotsAttribute()
    {
        return max(0, $this->max_devices - $this->active_devices);
    }

    /**
     * Get device usage percentage
     */
    public function getDeviceUsagePercentageAttribute()
    {
        if ($this->max_devices == 0) {
            return 0;
        }
        
        return round(($this->active_devices / $this->max_devices) * 100, 1);
    }

    /**
     * Update active device count
     */
    public function updateActiveDeviceCount()
    {
        $count = $this->posDevices()->where('is_activated', true)->count();
        $this->update(['active_devices' => $count]);
        return $count;
    }

    /**
     * Check if company license is valid
     */
    public function hasValidLicense()
    {
        return $this->status === 'active' && 
               ($this->license_expiry === null || $this->license_expiry->isFuture());
    }

    /**
     * Get company status with device info
     */
    public function getStatusWithDevicesAttribute()
    {
        $status = [
            'company_status' => $this->status,
            'license_valid' => $this->hasValidLicense(),
            'device_usage' => "{$this->active_devices}/{$this->max_devices}",
            'device_percentage' => $this->device_usage_percentage,
            'can_activate_more' => $this->canActivateMoreDevices()
        ];

        if ($this->license_expiry) {
            $status['license_expires'] = $this->license_expiry->format('Y-m-d');
            $status['days_until_expiry'] = $this->license_expiry->diffInDays(now());
        }

        return $status;
    }
}