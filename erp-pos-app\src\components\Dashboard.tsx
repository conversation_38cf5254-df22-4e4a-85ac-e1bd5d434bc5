import React, { useState, useEffect } from 'react';
import Sync<PERSON>anager from './SyncManager';

interface User {
  id: number;
  name: string;
  email: string;
  role?: string;
}

interface DashboardProps {
  user: User;
  token: string;
  onLogout: () => void;
}

const Dashboard: React.FC<DashboardProps> = ({ user, token, onLogout }) => {
  const [syncStatus, setSyncStatus] = useState('Not synced');
  const [inventoryCount, setInventoryCount] = useState(0);
  const [lastSync, setLastSync] = useState<string | null>(null);
  const [inventoryItems, setInventoryItems] = useState<any[]>([]);
  const [showInventory, setShowInventory] = useState(false);
  const [screens, setScreens] = useState<any[]>([]);
  const [showScreens, setShowScreens] = useState(false);

  const handleLogout = () => {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');
    localStorage.removeItem('logged_in');
    onLogout();
  };

  const testConnection = async () => {
    console.log('Testing connection...');
    console.log('Server URL:', localStorage.getItem('erp_server_url'));
    console.log('Token:', token);

    try {
      const serverUrl = localStorage.getItem('erp_server_url');
      const response = await fetch(`${serverUrl}/api/ping`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json',
        },
      });

      console.log('Ping response status:', response.status);
      
      if (response.ok) {
        const data = await response.json();
        console.log('Ping response data:', data);
        setSyncStatus('Connection successful');
      } else {
        setSyncStatus('Connection failed');
      }
    } catch (error) {
      console.error('Connection error:', error);
      setSyncStatus('Connection error');
    }
  };

  const syncInventory = async () => {
    console.log('Starting inventory sync...');
    console.log('Server URL:', localStorage.getItem('erp_server_url'));
    console.log('Token:', token);

    try {
      const serverUrl = localStorage.getItem('erp_server_url');
      const response = await fetch(`${serverUrl}/api/inventory`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json',
        },
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      if (response.ok) {
        const data = await response.json();
        console.log('Inventory data:', data);
        
        if (data.success && Array.isArray(data.data)) {
          setInventoryCount(data.data.length);
          setInventoryItems(data.data);
          setSyncStatus('Inventory synced');
          setLastSync(new Date().toLocaleString());
        } else {
          setSyncStatus('Sync failed: Invalid data format');
        }
      } else {
        const errorText = await response.text();
        console.error('Response error:', errorText);
        
        // If unauthorized, clear token and force re-login
        if (response.status === 401) {
          localStorage.removeItem('auth_token');
          localStorage.removeItem('user_data');
          localStorage.removeItem('logged_in');
          setSyncStatus('Session expired - please login again');
          // Trigger logout after a short delay
          setTimeout(() => {
            onLogout();
          }, 2000);
        } else {
          setSyncStatus(`Sync failed: ${response.status}`);
        }
      }
    } catch (error) {
      console.error('Sync error:', error);
      setSyncStatus(`Sync error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const syncScreens = async () => {
    console.log('Starting screens sync...');
    console.log('Server URL:', localStorage.getItem('erp_server_url'));
    console.log('Token:', token);

    try {
      const serverUrl = localStorage.getItem('erp_server_url');
      const response = await fetch(`${serverUrl}/api/screens`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json',
        },
      });

      console.log('Screens response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('Screens data:', data);

        if (data.success && Array.isArray(data.data)) {
          setScreens(data.data);
          setSyncStatus('Screens synced');
          setLastSync(new Date().toLocaleString());
        } else {
          setSyncStatus('Screens sync failed: Invalid data format');
        }
      } else {
        const errorText = await response.text();
        console.error('Screens response error:', errorText);

        if (response.status === 401) {
          localStorage.removeItem('auth_token');
          localStorage.removeItem('user_data');
          localStorage.removeItem('logged_in');
          setSyncStatus('Session expired - please login again');
          setTimeout(() => {
            onLogout();
          }, 2000);
        } else {
          setSyncStatus(`Screens sync failed: ${response.status}`);
        }
      }
    } catch (error) {
      console.error('Screens sync error:', error);
      setSyncStatus(`Screens sync error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  useEffect(() => {
    // Auto-test connection on load
    testConnection();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      {/* Header */}
      <header className="bg-black/20 backdrop-blur-lg border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">E</span>
              </div>
              <h1 className="text-xl font-bold text-white">ERP POS System</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm font-medium text-white">Welcome, {user?.name || user?.email}</p>
                <p className="text-xs text-white/60">{user?.role || 'User'}</p>
              </div>
              <button
                onClick={handleLogout}
                className="bg-red-500/80 hover:bg-red-500 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 backdrop-blur-sm border border-red-400/30"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-8 sm:px-6 lg:px-8">
        <div className="px-4 sm:px-0">
          {/* Status Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 p-6 shadow-xl">
              <div className="text-center">
                <h3 className="text-white/70 text-sm font-medium uppercase tracking-wider mb-2">
                  Sync Status
                </h3>
                <p className="text-2xl font-bold text-white">
                  {syncStatus}
                </p>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 p-6 shadow-xl">
              <div className="text-center">
                <h3 className="text-white/70 text-sm font-medium uppercase tracking-wider mb-2">
                  Inventory Items
                </h3>
                <p className="text-2xl font-bold text-white">
                  {inventoryCount.toLocaleString()}
                </p>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 p-6 shadow-xl">
              <div className="text-center">
                <h3 className="text-white/70 text-sm font-medium uppercase tracking-wider mb-2">
                  Last Sync
                </h3>
                <p className="text-2xl font-bold text-white">
                  {lastSync || 'Never'}
                </p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 mb-8 border border-white/20 shadow-xl">
            <h2 className="text-2xl font-bold text-white mb-8 text-center">
              Quick Actions
            </h2>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
              <button
                onClick={testConnection}
                className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white px-4 py-3 rounded-lg font-medium transition-all duration-200 transform hover:scale-[1.02] hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-transparent text-sm"
              >
                Test Connection
              </button>
              <button
                onClick={syncInventory}
                className="bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white px-4 py-3 rounded-lg font-medium transition-all duration-200 transform hover:scale-[1.02] hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:ring-offset-2 focus:ring-offset-transparent text-sm"
              >
                Sync Inventory
              </button>
              <button
                onClick={syncScreens}
                className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white px-4 py-3 rounded-lg font-medium transition-all duration-200 transform hover:scale-[1.02] hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:ring-offset-2 focus:ring-offset-transparent text-sm"
              >
                Sync Screens
              </button>
              <button
                onClick={() => setShowInventory(!showInventory)}
                className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-4 py-3 rounded-lg font-medium transition-all duration-200 transform hover:scale-[1.02] hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 focus:ring-offset-transparent text-sm"
              >
                {showInventory ? 'Hide Items' : 'View Items'}
              </button>
              <button
                onClick={() => setShowScreens(!showScreens)}
                className="bg-gradient-to-r from-rose-500 to-pink-500 hover:from-rose-600 hover:to-pink-600 text-white px-4 py-3 rounded-lg font-medium transition-all duration-200 transform hover:scale-[1.02] hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-rose-400 focus:ring-offset-2 focus:ring-offset-transparent text-sm"
              >
                {showScreens ? 'Hide Screens' : 'View Screens'}
              </button>
            </div>
          </div>

          {/* Inventory Items Display */}
          {showInventory && inventoryItems.length > 0 && (
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 mb-8 border border-white/20 shadow-xl">
              <h2 className="text-2xl font-bold text-white mb-8 text-center">
                Inventory Items ({inventoryItems.length})
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 max-h-96 overflow-y-auto">
                {inventoryItems.map((item, index) => (
                  <div key={index} className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 hover:bg-white/20 transition-all duration-200">
                    <div className="text-center">
                      <h3 className="text-white font-semibold text-sm mb-2 truncate" title={item.name}>
                        {item.name}
                      </h3>
                      <p className="text-white/70 text-xs mb-1">SKU: {item.sku}</p>
                      <p className="text-white/70 text-xs mb-2">Stock: {item.stock_quantity || 0}</p>
                      <p className="text-emerald-300 font-bold text-sm">
                        ${parseFloat(item.price || 0).toFixed(2)}
                      </p>
                      {item.category && (
                        <p className="text-white/50 text-xs mt-1 bg-white/10 rounded-full px-2 py-1">
                          {item.category}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Screens Display */}
          {showScreens && screens.length > 0 && (
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 mb-8 border border-white/20 shadow-xl">
              <h2 className="text-2xl font-bold text-white mb-8 text-center">
                POS Screens ({screens.length})
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {screens.map((screen, index) => (
                  <div key={index} className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-200">
                    <div className="text-center mb-4">
                      <h3 className="text-white font-bold text-lg mb-2" title={screen.name}>
                        {screen.name}
                      </h3>
                      <p className="text-white/70 text-sm mb-3">{screen.description}</p>
                      <div className="flex justify-center space-x-2 mb-3">
                        <span className="bg-indigo-500/30 text-indigo-200 px-3 py-1 rounded-full text-xs font-medium">
                          {screen.screen_type}
                        </span>
                        <span className="bg-emerald-500/30 text-emerald-200 px-3 py-1 rounded-full text-xs font-medium">
                          {screen.group?.products?.length || 0} items
                        </span>
                      </div>
                    </div>

                    {/* Screen Preview */}
                    <div
                      className="w-full h-32 rounded-lg border-2 border-white/20 flex items-center justify-center text-white/60 text-sm"
                      style={{ backgroundColor: screen.background_color + '20' }}
                    >
                      <div className="text-center">
                        <p className="font-medium">{screen.screen_type.toUpperCase()} LAYOUT</p>
                        <p className="text-xs mt-1">Group: {screen.group?.name}</p>
                      </div>
                    </div>

                    {/* Layout Info */}
                    {screen.layout_data && (
                      <div className="mt-4 text-xs text-white/60">
                        {screen.screen_type === 'grid' && (
                          <p>Grid: {screen.layout_data.columns}×{screen.layout_data.rows}</p>
                        )}
                        {screen.screen_type === 'list' && (
                          <p>List view with {screen.layout_data.show_images ? 'images' : 'text only'}</p>
                        )}
                        {screen.screen_type === 'category' && (
                          <p>Category view {screen.layout_data.collapsible_categories ? '(collapsible)' : ''}</p>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Sync Manager Component */}
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20 shadow-xl">
            <h2 className="text-2xl font-bold text-white mb-8 text-center">
              Sync Manager
            </h2>
            <SyncManager />
          </div>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
