/**
 * Role-Based Access Control Configuration
 * Defines what each user role can do in the POS system
 */

export interface Permission {
  id: string;
  name: string;
  description: string;
  category: 'sales' | 'products' | 'reports' | 'system' | 'users';
}

export interface Role {
  id: string;
  name: string;
  displayName: string;
  description: string;
  permissions: string[];
  color: string;
  icon: string;
}

// Define all available permissions
export const PERMISSIONS: Permission[] = [
  // Sales Permissions
  {
    id: 'sales.create',
    name: 'Create Sales',
    description: 'Process sales transactions and generate receipts',
    category: 'sales'
  },
  {
    id: 'sales.view',
    name: 'View Sales',
    description: 'View sales history and transaction details',
    category: 'sales'
  },
  {
    id: 'sales.refund',
    name: 'Process Refunds',
    description: 'Process returns and refunds',
    category: 'sales'
  },
  {
    id: 'sales.discount',
    name: 'Apply Discounts',
    description: 'Apply discounts to items and total sales',
    category: 'sales'
  },
  {
    id: 'sales.void',
    name: 'Void Transactions',
    description: 'Cancel or void sales transactions',
    category: 'sales'
  },

  // Product Permissions
  {
    id: 'products.view',
    name: 'View Products',
    description: 'View product catalog and inventory',
    category: 'products'
  },
  {
    id: 'products.sync',
    name: 'Sync Products',
    description: 'Synchronize products from ERP system',
    category: 'products'
  },
  {
    id: 'products.manage',
    name: 'Manage Products',
    description: 'Add, edit, and manage product information',
    category: 'products'
  },

  // Reports Permissions
  {
    id: 'reports.daily',
    name: 'Daily Reports',
    description: 'Generate and view daily sales reports',
    category: 'reports'
  },
  {
    id: 'reports.end_of_day',
    name: 'End of Day (Z-Report)',
    description: 'Generate end-of-day reports and close register',
    category: 'reports'
  },
  {
    id: 'reports.analytics',
    name: 'Sales Analytics',
    description: 'View detailed sales analytics and trends',
    category: 'reports'
  },
  {
    id: 'reports.export',
    name: 'Export Reports',
    description: 'Export reports to PDF, Excel, or other formats',
    category: 'reports'
  },

  // System Permissions
  {
    id: 'system.settings',
    name: 'System Settings',
    description: 'Access and modify system settings',
    category: 'system'
  },
  {
    id: 'system.backup',
    name: 'Backup & Restore',
    description: 'Create backups and restore data',
    category: 'system'
  },
  {
    id: 'system.sync',
    name: 'Data Synchronization',
    description: 'Manage data sync with ERP system',
    category: 'system'
  },
  {
    id: 'system.maintenance',
    name: 'System Maintenance',
    description: 'Perform system maintenance tasks',
    category: 'system'
  },

  // User Management Permissions
  {
    id: 'users.view',
    name: 'View Users',
    description: 'View user accounts and profiles',
    category: 'users'
  },
  {
    id: 'users.manage',
    name: 'Manage Users',
    description: 'Create, edit, and manage user accounts',
    category: 'users'
  },
  {
    id: 'users.roles',
    name: 'Manage Roles',
    description: 'Assign and modify user roles',
    category: 'users'
  }
];

// Define user roles with their permissions
export const ROLES: Role[] = [
  {
    id: 'cashier',
    name: 'cashier',
    displayName: 'Cashier / POS User',
    description: 'Basic POS operations - sales transactions only',
    color: '#28a745',
    icon: '💰',
    permissions: [
      'sales.create',
      'sales.view',
      'products.view'
    ]
  },
  {
    id: 'pos_user',
    name: 'pos_user',
    displayName: 'POS User',
    description: 'Standard POS operations with limited discounts',
    color: '#17a2b8',
    icon: '🛒',
    permissions: [
      'sales.create',
      'sales.view',
      'sales.discount',
      'products.view'
    ]
  },
  {
    id: 'manager',
    name: 'manager',
    displayName: 'Manager',
    description: 'Sales operations + product sync + end-of-day reports',
    color: '#fd7e14',
    icon: '👨‍💼',
    permissions: [
      'sales.create',
      'sales.view',
      'sales.refund',
      'sales.discount',
      'sales.void',
      'products.view',
      'products.sync',
      'reports.daily',
      'reports.end_of_day',
      'reports.analytics',
      'reports.export',
      'system.sync'
    ]
  },
  {
    id: 'admin',
    name: 'admin',
    displayName: 'Admin',
    description: 'Full POS control + user management (company level)',
    color: '#6f42c1',
    icon: '👨‍💻',
    permissions: [
      'sales.create',
      'sales.view',
      'sales.refund',
      'sales.discount',
      'sales.void',
      'products.view',
      'products.sync',
      'products.manage',
      'reports.daily',
      'reports.end_of_day',
      'reports.analytics',
      'reports.export',
      'system.settings',
      'system.backup',
      'system.sync',
      'users.view',
      'users.manage'
    ]
  },
  {
    id: 'master_admin',
    name: 'master_admin',
    displayName: 'Master Admin',
    description: 'Full ERP control + system administration',
    color: '#dc3545',
    icon: '👑',
    permissions: [
      // All permissions
      ...PERMISSIONS.map(p => p.id)
    ]
  }
];

/**
 * Check if a user has a specific permission
 */
export const hasPermission = (userRole: string, permission: string): boolean => {
  const role = ROLES.find(r => r.name === userRole || r.id === userRole);
  return role ? role.permissions.includes(permission) : false;
};

/**
 * Get all permissions for a user role
 */
export const getRolePermissions = (userRole: string): Permission[] => {
  const role = ROLES.find(r => r.name === userRole || r.id === userRole);
  if (!role) return [];
  
  return PERMISSIONS.filter(p => role.permissions.includes(p.id));
};

/**
 * Get role information
 */
export const getRoleInfo = (userRole: string): Role | null => {
  return ROLES.find(r => r.name === userRole || r.id === userRole) || null;
};

/**
 * Get permissions by category for a role
 */
export const getRolePermissionsByCategory = (userRole: string): Record<string, Permission[]> => {
  const permissions = getRolePermissions(userRole);
  
  return permissions.reduce((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = [];
    }
    acc[permission.category].push(permission);
    return acc;
  }, {} as Record<string, Permission[]>);
};

/**
 * Check if user can access a specific feature
 */
export const canAccess = {
  // Sales Features
  createSale: (role: string) => hasPermission(role, 'sales.create'),
  viewSales: (role: string) => hasPermission(role, 'sales.view'),
  processRefund: (role: string) => hasPermission(role, 'sales.refund'),
  applyDiscount: (role: string) => hasPermission(role, 'sales.discount'),
  voidTransaction: (role: string) => hasPermission(role, 'sales.void'),
  
  // Product Features
  viewProducts: (role: string) => hasPermission(role, 'products.view'),
  syncProducts: (role: string) => hasPermission(role, 'products.sync'),
  manageProducts: (role: string) => hasPermission(role, 'products.manage'),
  
  // Report Features
  dailyReports: (role: string) => hasPermission(role, 'reports.daily'),
  endOfDay: (role: string) => hasPermission(role, 'reports.end_of_day'),
  analytics: (role: string) => hasPermission(role, 'reports.analytics'),
  exportReports: (role: string) => hasPermission(role, 'reports.export'),
  
  // System Features
  systemSettings: (role: string) => hasPermission(role, 'system.settings'),
  backup: (role: string) => hasPermission(role, 'system.backup'),
  dataSync: (role: string) => hasPermission(role, 'system.sync'),
  maintenance: (role: string) => hasPermission(role, 'system.maintenance'),
  
  // User Management
  viewUsers: (role: string) => hasPermission(role, 'users.view'),
  manageUsers: (role: string) => hasPermission(role, 'users.manage'),
  manageRoles: (role: string) => hasPermission(role, 'users.roles')
};

export default {
  PERMISSIONS,
  ROLES,
  hasPermission,
  getRolePermissions,
  getRoleInfo,
  getRolePermissionsByCategory,
  canAccess
};
