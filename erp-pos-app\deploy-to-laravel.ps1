# Deploy React POS App to Laravel Backend
Write-Host "🚀 Deploying React POS App to Laravel Backend..." -ForegroundColor Green

# Build the React app
Write-Host "📦 Building React application..." -ForegroundColor Yellow
npm run build

# Check if build was successful
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed! Please fix the errors and try again." -ForegroundColor Red
    exit 1
}

# Copy built files to Laravel public directory
Write-Host "📁 Copying built files to <PERSON><PERSON> backend..." -ForegroundColor Yellow

# Create POS directory in Laravel public folder
$targetDir = "..\erp-pos-system\public\pos"
if (!(Test-Path $targetDir)) {
    New-Item -ItemType Directory -Path $targetDir -Force
}

# Copy all built files
Copy-Item -Path "dist\*" -Destination $targetDir -Recurse -Force

# Update the index.html to use correct asset paths
Write-Host "🔧 Updating asset paths..." -ForegroundColor Yellow
$indexPath = "$targetDir\index.html"
if (Test-Path $indexPath) {
    $content = Get-Content $indexPath -Raw
    $content = $content -replace '/assets/', '/pos/assets/'
    Set-Content -Path $indexPath -Value $content
}

Write-Host "✅ Deployment completed successfully!" -ForegroundColor Green
Write-Host "🌐 POS App is now available at: /pos" -ForegroundColor Cyan
Write-Host "📱 Access it from your Laravel application at: http://your-domain.com/pos" -ForegroundColor Cyan
