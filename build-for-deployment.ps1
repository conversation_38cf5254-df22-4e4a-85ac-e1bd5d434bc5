# Build ERP POS System for Deployment
Write-Host "🚀 Building ERP POS System for deployment..." -ForegroundColor Green

# Build React POS App
Write-Host "📱 Building React POS App..." -ForegroundColor Yellow
Set-Location erp-pos-app
npm install
npm run build

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ React build failed!" -ForegroundColor Red
    exit 1
}

# Deploy React app to Laravel public directory
Write-Host "📁 Deploying React app to Laravel..." -ForegroundColor Yellow
$targetDir = "..\erp-pos-system\public\pos"
if (!(Test-Path $targetDir)) {
    New-Item -ItemType Directory -Path $targetDir -Force
}

Copy-Item -Path "dist\*" -Destination $targetDir -Recurse -Force

# Update asset paths
$indexPath = "$targetDir\index.html"
if (Test-Path $indexPath) {
    $content = Get-Content $indexPath -Raw
    $content = $content -replace '/assets/', '/pos/assets/'
    Set-Content -Path $indexPath -Value $content
}

Set-Location ..

# Prepare Lara<PERSON> backend
Write-Host "🔧 Preparing Laravel backend..." -ForegroundColor Yellow
Set-Location erp-pos-system

# Install PHP dependencies
composer install --no-dev --optimize-autoloader

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Composer install failed!" -ForegroundColor Red
    exit 1
}

# Clear and cache configurations
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear

Write-Host "✅ Build completed successfully!" -ForegroundColor Green
Write-Host "🌐 Ready for deployment to Render.com" -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Yellow
Write-Host "1. Commit all changes to Git"
Write-Host "2. Push to your repository"
Write-Host "3. Deploy to Render.com using render.yaml"
Write-Host ""
Write-Host "🔗 Your app will be available at:" -ForegroundColor Cyan
Write-Host "   - Backend: https://erp-pos-system.onrender.com"
Write-Host "   - POS App: https://erp-pos-system.onrender.com/pos"

Set-Location ..
