<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Screen extends Model
{
    use HasFactory;

    protected $fillable = [
        'group_id',
        'name',
        'description',
        'screen_type',
        'layout_data',
        'is_active',
        'sort_order',
        'background_color',
        'text_color'
    ];

    protected $casts = [
        'layout_data' => 'array',
        'is_active' => 'boolean',
    ];

    public function group()
    {
        return $this->belongsTo(Group::class);
    }

    public function products()
    {
        return $this->hasManyThrough(Product::class, Group::class);
    }
}
