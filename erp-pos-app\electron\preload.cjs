// preload.cjs - Expose IPC handlers to renderer process
const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electron', {
  // ERP API functions
  erp: {
    testConnection: () => ipcRenderer.invoke('test-erp-connection'),
    login: (username, password) => ipc<PERSON>enderer.invoke('login', username, password),
    syncInventory: () => ipcRenderer.invoke('sync-inventory'),
    syncSales: () => ipcRenderer.invoke('sync-sales')
  },
  
  // Database functions - you can add more as needed
  db: {
    getProducts: () => ipcRenderer.invoke('db-get-products'),
    getProduct: (id) => ipcRenderer.invoke('db-get-product', id),
    createSale: (saleData) => ipcRenderer.invoke('db-create-sale', saleData)
  },
  
  // System functions
  system: {
    getPrinters: () => ipc<PERSON>ender<PERSON>.invoke('system-get-printers'),
    print: (data) => ipc<PERSON>enderer.invoke('system-print', data)
  }
});
