﻿/// <reference types="vite/client" />

interface SyncResult {
  success?: boolean;
  connected?: boolean;
  count?: number;
  total?: number;
  error?: string;
  message?: string;
  data?: any;
}

interface Window {
  electron: {
    erp: {
      testConnection: () => Promise<SyncResult>;
      login: (username: string, password: string) => Promise<SyncResult>;
      syncInventory: () => Promise<SyncResult>;
      syncSales: () => Promise<SyncResult>;
    };
    db: {
      getProducts: () => Promise<any[]>;
      getProduct: (id: number) => Promise<any>;
      createSale: (saleData: any) => Promise<any>;
    };
    system: {
      getPrinters: () => Promise<string[]>;
      print: (data: any) => Promise<boolean>;
    };
  };
}
