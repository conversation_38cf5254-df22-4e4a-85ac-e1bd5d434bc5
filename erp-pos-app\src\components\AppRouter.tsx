/**
 * App Router - Handles routing based on device activation status
 */

import React, { useEffect, useState } from 'react';
import { useDeviceActivation } from '../hooks/useDeviceActivation';
import LoginPage from './LoginPage';
import ActivationPage from './ActivationPage';
import UserSelection from './UserSelection';
import LoadingScreen from './LoadingScreen';
import POSInterface from './POSInterface';
import EndOfDay from './EndOfDay';
import AdminPanel from './AdminPanel';
import { dataSyncService } from '../services/dataSyncService';
import { canAccess } from '../config/roles';

interface AppRouterProps {
  // Add any props you need
}

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
}

const AppRouter: React.FC<AppRouterProps> = () => {
  const { isActivated, isLoading, deviceInfo, error } = useDeviceActivation();
  const [currentScreen, setCurrentScreen] = useState<'loading' | 'activation' | 'login' | 'pin-login' | 'pos' | 'end-of-day'>('loading');
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);
  const [serverUrl, setServerUrl] = useState<string>('');
  const [companyData, setCompanyData] = useState<any>(null);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [authToken, setAuthToken] = useState<string>('');
  const [, setProducts] = useState<any[]>([]);
  const [, setSyncStatus] = useState<any>(null);
  const [showAdminPanel, setShowAdminPanel] = useState(false);

  /**
   * Add keyboard shortcut for admin panel (Ctrl+Shift+A)
   */
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'A') {
        event.preventDefault();
        setShowAdminPanel(true);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  /**
   * Check for stored activation data and initialize data sync on component mount
   */
  useEffect(() => {
    const storedServerUrl = localStorage.getItem('erp_server_url');
    const storedCompanyData = localStorage.getItem('company_data');
    const storedLoggedIn = localStorage.getItem('logged_in');
    const storedUserData = localStorage.getItem('user_data');
    const storedAuthToken = localStorage.getItem('auth_token');

    if (storedServerUrl) {
      setServerUrl(storedServerUrl);
    }

    if (storedCompanyData) {
      try {
        setCompanyData(JSON.parse(storedCompanyData));
      } catch (e) {
        console.error('Error parsing stored company data:', e);
      }
    }

    if (storedLoggedIn === 'true' && storedUserData && storedAuthToken) {
      try {
        setCurrentUser(JSON.parse(storedUserData));
        setAuthToken(storedAuthToken);
        setIsLoggedIn(true);
      } catch (e) {
        console.error('Error parsing stored user data:', e);
        // Clear invalid stored data
        localStorage.removeItem('logged_in');
        localStorage.removeItem('user_data');
        localStorage.removeItem('auth_token');
      }
    }

    // Initialize data sync service and load local products
    const initializeDataSync = async () => {
      try {
        const localProducts = dataSyncService.getLocalProducts();
        setProducts(localProducts);

        const status = dataSyncService.getSyncStatus();
        setSyncStatus(status);

        // Note: Product sync will happen after authentication
        // We don't sync products here because we don't have auth token yet
      } catch (error) {
        console.error('Error initializing data sync:', error);
      }
    };

    initializeDataSync();
  }, []);

  /**
   * Determine which screen to show based on activation and login status
   */
  useEffect(() => {
    if (isLoading) {
      setCurrentScreen('loading');
      return;
    }

    // Check for one-time device activation first
    const deviceActivation = localStorage.getItem('device_activation');
    const deviceFingerprint = localStorage.getItem('device_fingerprint');

    if (deviceActivation && deviceFingerprint) {
      try {
        const activation = JSON.parse(deviceActivation);
        const fingerprint = JSON.parse(deviceFingerprint);

        // Verify activation integrity
        if (activation.isActivated &&
            activation.macAddress &&
            fingerprint.macAddress === activation.macAddress &&
            activation.activatedAt) {

          // Device is already activated - skip activation screen entirely
          console.log('Device already activated:', activation.companyInfo?.name);

          // Set company data from activation
          if (!companyData && activation.companyInfo) {
            setCompanyData(activation.companyInfo);
            localStorage.setItem('company_data', JSON.stringify(activation.companyInfo));
          }

          // Set server URL from activation
          if (!serverUrl && activation.companyInfo?.server_url) {
            setServerUrl(activation.companyInfo.server_url);
            localStorage.setItem('erp_server_url', activation.companyInfo.server_url);
          }

          // Skip to login screen
          if (!isLoggedIn) {
            setCurrentScreen('pin-login');
            return;
          }
        }
      } catch (error) {
        console.error('Error verifying device activation:', error);
        // Clear corrupted activation data
        localStorage.removeItem('device_activation');
        localStorage.removeItem('device_fingerprint');
      }
    }

    // Check if we have stored activation data (legacy support)
    const hasActivationData = serverUrl && companyData;

    if (!isActivated && !hasActivationData && !deviceActivation) {
      // Device not activated and no stored data - show activation screen
      setCurrentScreen('activation');
      return;
    }

    if ((isActivated || hasActivationData) && !isLoggedIn) {
      // Device activated but user not logged in - show PIN login screen
      setCurrentScreen('pin-login');
      return;
    }

    if ((isActivated || hasActivationData) && isLoggedIn) {
      // Device activated and user logged in - show POS interface
      setCurrentScreen('pos');
      return;
    }
  }, [isActivated, isLoading, isLoggedIn, serverUrl, companyData]);

  /**
   * Handle successful activation
   */
  const handleActivationComplete = (url: string, company: any) => {
    setServerUrl(url);
    setCompanyData(company);
    // Persist activation data
    localStorage.setItem('erp_server_url', url);
    localStorage.setItem('company_data', JSON.stringify(company));
    localStorage.setItem('app_activated', 'true');
    // The screen will automatically switch to PIN login based on useEffect
  };

  /**
   * Handle successful email/password login (from activation flow)
   */
  const handleLoginSuccess = (userData: any, token: string) => {
    setCurrentUser(userData);
    setAuthToken(token);
    setIsLoggedIn(true);
    // Persist login data
    localStorage.setItem('user_data', JSON.stringify(userData));
    localStorage.setItem('auth_token', token);
    localStorage.setItem('logged_in', 'true');
    console.log('User logged in via email/password:', userData);
  };

  /**
   * Handle successful PIN login
   */
  const handlePinLoginSuccess = (userData: User, token: string) => {
    setCurrentUser(userData);
    setAuthToken(token);
    setIsLoggedIn(true);
    // Persist login data
    localStorage.setItem('user_data', JSON.stringify(userData));
    localStorage.setItem('auth_token', token);
    localStorage.setItem('logged_in', 'true');

    // Initialize data sync with auth token
    dataSyncService.setAuthToken(token);

    console.log('User logged in via PIN:', userData);
  };

  /**
   * Handle logout
   */
  const handleLogout = () => {
    setIsLoggedIn(false);
    setCurrentUser(null);
    setAuthToken('');
    // Clear stored login data
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');
    localStorage.removeItem('logged_in');
  };

  /**
   * Handle back to activation (from login screen)
   */
  const handleBackToActivation = () => {
    // Clear activation data
    localStorage.removeItem('erp_server_url');
    localStorage.removeItem('company_data');
    localStorage.removeItem('app_activated');
    setServerUrl('');
    setCompanyData(null);
    setCurrentScreen('activation');
  };

  /**
   * Handle navigation to End of Day
   */
  const handleShowEndOfDay = () => {
    // Check if user has permission for end-of-day reports
    if (currentUser && canAccess.endOfDay(currentUser.role)) {
      setCurrentScreen('end-of-day');
    } else {
      alert('You do not have permission to access end-of-day reports');
    }
  };

  /**
   * Handle back to POS from End of Day
   */
  const handleBackToPOS = () => {
    setCurrentScreen('pos');
    // Reset the POS state by forcing a re-render with a new key
    setPosKey(prev => prev + 1);
  };

  // Key to force POS component reset
  const [posKey, setPosKey] = useState(0);

  /**
   * Render appropriate screen based on current state
   */
  const renderCurrentScreen = () => {
    switch (currentScreen) {
      case 'loading':
        return (
          <LoadingScreen
            message="Checking device status..."
            error={error}
          />
        );

      case 'activation':
        return (
          <ActivationPage
            onActivationComplete={handleActivationComplete}
          />
        );

      case 'login':
        return (
          <LoginPage 
            serverUrl={serverUrl}
            companyData={companyData}
            onLoginSuccess={handleLoginSuccess}
            onBackToActivation={handleBackToActivation}
          />
        );

      case 'pin-login':
        return (
          <UserSelection 
            onLogin={handlePinLoginSuccess}
            onBackToActivation={handleBackToActivation}
          />
        );

      case 'pos':
        if (!currentUser) {
          setCurrentScreen('pin-login');
          return null;
        }
        return (
          <POSInterface
            key={posKey}
            onLogout={handleLogout}
            onShowEndOfDay={handleShowEndOfDay}
            deviceInfo={deviceInfo}
            user={currentUser}
            token={authToken}
          />
        );

      case 'end-of-day':
        if (!currentUser) {
          // Redirect to login if user is somehow missing
          setCurrentScreen('pin-login');
          return null;
        }
        return (
          <EndOfDay
            user={currentUser}
            token={authToken}
            onBack={handleBackToPOS}
            onLogout={handleLogout}
          />
        );

      default:
        return (
          <LoadingScreen 
            message="Initializing..." 
            error="Unknown application state"
          />
        );
    }
  };

  return (
    <div className="app-container h-screen w-screen overflow-hidden">
      {renderCurrentScreen()}

      {/* Admin Panel - accessible with Ctrl+Shift+A */}
      {showAdminPanel && (
        <AdminPanel onClose={() => setShowAdminPanel(false)} />
      )}
    </div>
  );
};

export default AppRouter;