# Master Admin Account Setup Documentation

## Overview
This document provides comprehensive documentation for the master admin account setup in the ERP POS system, including the newly added master admin account with password "123123".

## Available Master Admin Accounts

### 1. Original Master Admin
- **Email:** <EMAIL>
- **Password:** master123
- **Role:** master_admin
- **ID:** 1 (forced ID for system consistency)
- **Company/Branch:** None (system-wide access)

### 2. New Master Admin (Added for convenience)
- **Email:** <EMAIL>
- **Password:** 123123
- **Role:** master_admin
- **Company/Branch:** None (system-wide access)
- **Purpose:** Easy-to-remember password for testing and development

## Database Seeder Information

### AuthSeeder Class
**Location:** `database/seeders/AuthSeeder.php`

### Seeder Contents
The AuthSeeder creates:
1. Two master admin accounts (as described above)
2. Two sample companies with complete organizational structure
3. Admin, manager, and cashier users for each company

### Running the Seeder

#### Fresh Installation
```bash
php artisan migrate --seed
```

#### Re-run Specific Seeder
```bash
php artisan db:seed --class=AuthSeeder
```

#### Refresh and Seed
```bash
php artisan migrate:refresh --seed
```

### Sample Companies Created

#### Company 1: ABC Retail Store
- **License Key:** ABC-LICENSE-2024
- **License Expiry:** 1 year from now
- **POS Limit:** 5 devices
- **Brand:** ABC Fashion
- **Branch:** Main Store

#### Company 2: XYZ Electronics
- **License Key:** XYZ-LICENSE-2024
- **License Expiry:** 6 months from now
- **POS Limit:** 3 devices
- **Brand:** XYZ Tech
- **Branch:** Electronics Store

## User Roles and Permissions

### Master Admin (master_admin)
- System-wide administrative access
- Can manage all companies, brands, branches, and users
- Can reset device MAC addresses and activation keys
- Access to all system settings and configurations

### Company Admin (admin)
- Company-level administrative access
- Can manage users within their company
- Can configure company settings and POS devices
- Access to company reports and analytics

### Manager (manager)
- Branch-level management access
- Can oversee sales and inventory
- Limited user management capabilities
- Access to branch-specific reports

### Cashier (cashier)
- Point-of-sale operation access
- Can process sales transactions
- Limited to assigned branch operations
- Basic inventory viewing capabilities

## Security Considerations

### Password Security
- All passwords are hashed using Laravel's bcrypt algorithm
- The "123123" password is provided for development/testing convenience
- In production, consider using stronger, unique passwords
- Regular password rotation is recommended for security

### Account Management
- Master admin accounts should be limited and closely monitored
- Regular audit of admin account activity is recommended
- Consider implementing 2-factor authentication for admin accounts

## Troubleshooting

### Common Issues

#### Seeder Fails to Run
```bash
# Clear cache and re-run
php artisan cache:clear
php artisan config:clear
php artisan db:seed --class=AuthSeeder
```

#### Password Not Working
- Ensure the seeder ran successfully
- Check that the password field contains the hashed value, not plain text
- Verify the User model uses Laravel's HasApiTokens trait if using API authentication

#### Duplicate Email Errors
- The seeder uses unique email addresses
- If encountering duplicates, truncate the users table before re-seeding

### Database Operations

#### View Created Users
```sql
SELECT id, name, email, role, company_id, branch_id FROM users;
```

#### Reset Specific User Password
```sql
UPDATE users SET password = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' WHERE email = '<EMAIL>';
```

## Best Practices

1. **Production Deployment:**
   - Change default passwords before going to production
   - Remove or disable the "123123" master admin account in production
   - Implement proper access controls and logging

2. **Development:**
   - Use the convenient "123123" password for testing
   - Keep the seeder updated with realistic test data
   - Document any changes to the seeder structure

3. **Security:**
   - Regularly review admin account access
   - Monitor login attempts and failed authentications
   - Implement session timeout for admin accounts

## Support
For issues with master admin accounts or the seeder functionality, refer to:
- Laravel documentation on database seeding
- System administrator guidelines
- Security best practices documentation

---
*Last Updated: [Current Date]*
*System Version: ERP POS System v1.0*
