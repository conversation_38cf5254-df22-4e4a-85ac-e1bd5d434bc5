import React, { useState, useEffect } from 'react';
import { macDeviceService } from '../services/macDeviceService';

interface AdminPanelProps {
  onClose: () => void;
}

const AdminPanel: React.FC<AdminPanelProps> = ({ onClose }) => {
  const [adminPassword, setAdminPassword] = useState('');
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [deviceInfo, setDeviceInfo] = useState<any>(null);

  useEffect(() => {
    // Load device information
    const loadDeviceInfo = () => {
      const activation = localStorage.getItem('device_activation');
      const fingerprint = localStorage.getItem('device_fingerprint');
      
      if (activation && fingerprint) {
        try {
          const activationData = JSON.parse(activation);
          const fingerprintData = JSON.parse(fingerprint);
          
          setDeviceInfo({
            isActivated: activationData.isActivated,
            macAddress: activationData.macAddress,
            companyId: activationData.companyId,
            companyName: activationData.companyInfo?.name,
            activatedAt: activationData.activatedAt,
            fingerprintMatch: fingerprintData.macAddress === activationData.macAddress
          });
        } catch (error) {
          console.error('Error parsing device info:', error);
        }
      } else {
        setDeviceInfo({
          isActivated: false,
          macAddress: macDeviceService.getMacAddressValue(),
          companyId: null,
          companyName: null,
          activatedAt: null,
          fingerprintMatch: false
        });
      }
    };

    loadDeviceInfo();
  }, []);

  const handleResetDevice = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!adminPassword.trim()) {
      setMessage('Please enter the master admin password');
      return;
    }

    setIsLoading(true);
    setMessage('');

    try {
      const result = await macDeviceService.clearOneTimeActivation(adminPassword);
      
      if (result.success) {
        setMessage('✅ ' + result.message);
        
        // Reload device info
        setTimeout(() => {
          window.location.reload();
        }, 3000);
      } else {
        setMessage('❌ ' + result.message);
      }
    } catch (error) {
      setMessage('❌ Error: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="admin-panel-overlay">
      <div className="admin-panel">
        <div className="admin-header">
          <h2>🔧 Master Admin Panel</h2>
          <button onClick={onClose} className="close-button">×</button>
        </div>

        <div className="device-status">
          <h3>📱 Device Status</h3>
          <div className="status-grid">
            <div className="status-item">
              <label>Activation Status:</label>
              <span className={deviceInfo?.isActivated ? 'status-active' : 'status-inactive'}>
                {deviceInfo?.isActivated ? '✅ Activated' : '❌ Not Activated'}
              </span>
            </div>
            
            <div className="status-item">
              <label>MAC Address:</label>
              <span className="mac-address">{deviceInfo?.macAddress || 'Unknown'}</span>
            </div>
            
            {deviceInfo?.isActivated && (
              <>
                <div className="status-item">
                  <label>Company ID:</label>
                  <span>{deviceInfo.companyId}</span>
                </div>
                
                <div className="status-item">
                  <label>Company Name:</label>
                  <span>{deviceInfo.companyName || 'Unknown'}</span>
                </div>
                
                <div className="status-item">
                  <label>Activated At:</label>
                  <span>{deviceInfo.activatedAt ? new Date(deviceInfo.activatedAt).toLocaleString() : 'Unknown'}</span>
                </div>
                
                <div className="status-item">
                  <label>Fingerprint Match:</label>
                  <span className={deviceInfo.fingerprintMatch ? 'status-active' : 'status-inactive'}>
                    {deviceInfo.fingerprintMatch ? '✅ Valid' : '❌ Invalid'}
                  </span>
                </div>
              </>
            )}
          </div>
        </div>

        {deviceInfo?.isActivated && (
          <div className="reset-section">
            <h3>⚠️ Reset Device Activation</h3>
            <p className="warning-text">
              <strong>WARNING:</strong> This will permanently remove the device activation and allow reactivation with a different company. 
              This action cannot be undone and should only be performed by master administrators.
            </p>
            
            <form onSubmit={handleResetDevice}>
              <div className="form-group">
                <label htmlFor="adminPassword">Master Admin Password:</label>
                <input
                  type="password"
                  id="adminPassword"
                  value={adminPassword}
                  onChange={(e) => setAdminPassword(e.target.value)}
                  placeholder="Enter master admin password"
                  disabled={isLoading}
                  required
                />
                <small>Contact system administrator for the master admin password</small>
              </div>

              {message && (
                <div className={`message ${message.startsWith('✅') ? 'success' : 'error'}`}>
                  {message}
                </div>
              )}

              <button 
                type="submit" 
                className="reset-button"
                disabled={isLoading}
              >
                {isLoading ? 'Resetting Device...' : 'Reset Device Activation'}
              </button>
            </form>
          </div>
        )}

        {!deviceInfo?.isActivated && (
          <div className="info-section">
            <h3>ℹ️ Device Not Activated</h3>
            <p>This device has not been activated yet. You can proceed with normal activation.</p>
          </div>
        )}

        <div className="admin-info">
          <h4>📋 Admin Information:</h4>
          <ul>
            <li>🔐 This panel is only accessible with Ctrl+Shift+A</li>
            <li>⚠️ Device reset requires master admin password</li>
            <li>🔄 After reset, the device can be reactivated with any company</li>
            <li>💾 Sales data is preserved during reset (optional)</li>
            <li>🔒 Only master administrators should have access to this function</li>
          </ul>
        </div>
      </div>

      <style>{`
        .admin-panel-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10000;
          padding: 20px;
        }

        .admin-panel {
          background: white;
          border-radius: 12px;
          padding: 30px;
          max-width: 600px;
          width: 100%;
          max-height: 90vh;
          overflow-y: auto;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .admin-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 30px;
          padding-bottom: 15px;
          border-bottom: 2px solid #eee;
        }

        .admin-header h2 {
          margin: 0;
          color: #333;
          font-size: 24px;
        }

        .close-button {
          background: #ff4757;
          color: white;
          border: none;
          border-radius: 50%;
          width: 30px;
          height: 30px;
          font-size: 18px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .device-status, .reset-section, .info-section {
          margin-bottom: 30px;
        }

        .device-status h3, .reset-section h3, .info-section h3 {
          color: #333;
          margin-bottom: 15px;
          font-size: 18px;
        }

        .status-grid {
          display: grid;
          gap: 12px;
        }

        .status-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px;
          background: #f8f9fa;
          border-radius: 6px;
        }

        .status-item label {
          font-weight: 600;
          color: #555;
        }

        .status-active {
          color: #28a745;
          font-weight: 600;
        }

        .status-inactive {
          color: #dc3545;
          font-weight: 600;
        }

        .mac-address {
          font-family: 'Courier New', monospace;
          background: #e9ecef;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 14px;
        }

        .warning-text {
          background: #fff3cd;
          border: 1px solid #ffeaa7;
          border-radius: 6px;
          padding: 15px;
          color: #856404;
          margin-bottom: 20px;
        }

        .form-group {
          margin-bottom: 20px;
        }

        .form-group label {
          display: block;
          font-weight: 600;
          color: #333;
          margin-bottom: 8px;
        }

        .form-group input {
          width: 100%;
          padding: 12px;
          border: 2px solid #ddd;
          border-radius: 6px;
          font-size: 16px;
          box-sizing: border-box;
        }

        .form-group input:focus {
          outline: none;
          border-color: #007bff;
        }

        .form-group small {
          display: block;
          margin-top: 6px;
          color: #666;
          font-size: 12px;
        }

        .message {
          padding: 12px;
          border-radius: 6px;
          margin-bottom: 20px;
          font-weight: 500;
        }

        .message.success {
          background: #d4edda;
          color: #155724;
          border: 1px solid #c3e6cb;
        }

        .message.error {
          background: #f8d7da;
          color: #721c24;
          border: 1px solid #f5c6cb;
        }

        .reset-button {
          width: 100%;
          background: #dc3545;
          color: white;
          border: none;
          padding: 15px;
          border-radius: 6px;
          font-size: 16px;
          font-weight: 600;
          cursor: pointer;
          transition: background 0.3s;
        }

        .reset-button:hover:not(:disabled) {
          background: #c82333;
        }

        .reset-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .admin-info {
          background: #e7f3ff;
          padding: 20px;
          border-radius: 8px;
          border-left: 4px solid #007bff;
        }

        .admin-info h4 {
          color: #333;
          margin-bottom: 15px;
        }

        .admin-info ul {
          list-style: none;
          padding: 0;
          margin: 0;
        }

        .admin-info li {
          padding: 6px 0;
          color: #555;
          font-size: 14px;
        }
      `}</style>
    </div>
  );
};

export default AdminPanel;
