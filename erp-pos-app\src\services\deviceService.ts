/**
 * Device Service - Handles POS device activation and fingerprinting
 */

export interface DeviceInfo {
  fingerprint: string;
  macAddress: string;
  hardwareInfo: {
    platform: string;
    arch: string;
    userAgent: string;
    screenResolution: string;
    timezone: string;
    language: string;
    cpuCores: number;
    memory: string;
  };
}

export interface ActivationStatus {
  activated: boolean;
  deviceId?: number;
  deviceName?: string;
  companyName?: string;
  branchName?: string;
  activatedAt?: string;
  lastSeen?: string;
  message?: string;
}

export interface ActivationResponse {
  success: boolean;
  message: string;
  device_info?: {
    device_id: number;
    device_name: string;
    branch_name: string;
    company_name: string;
    activated_at: string;
  };
  redirect_url?: string;
}

class DeviceService {
  private readonly STORAGE_KEY = 'pos_device_fingerprint';
  private readonly API_BASE_URL = 'http://127.0.0.1:8000/api/pos';
  private deviceFingerprint: string | null = null;

  /**
   * Initialize device service and check activation status
   */
  async initialize(): Promise<ActivationStatus> {
    try {
      // Generate or retrieve device fingerprint
      this.deviceFingerprint = await this.getOrGenerateFingerprint();
      
      // Check if device is activated
      const status = await this.checkActivationStatus();
      
      // Store fingerprint if device is activated
      if (status.activated && this.deviceFingerprint) {
        localStorage.setItem(this.STORAGE_KEY, this.deviceFingerprint);
      }
      
      return status;
    } catch (error) {
      console.error('Failed to initialize device service:', error);
      console.log('Attempting to retrieve MAC address...');
      return {
        activated: false,
        message: 'Failed to check device status'
      };
    }
  }

  /**
   * Generate unique device fingerprint
   */
  private async generateDeviceFingerprint(): Promise<string> {
    const components = [
      navigator.platform,
      navigator.userAgent,
      screen.width + 'x' + screen.height,
      navigator.hardwareConcurrency?.toString() || '0',
      navigator.language,
      window.location.hostname,
      new Date().getTimezoneOffset().toString(),
    ];

    // Create hash from components
    const encoder = new TextEncoder();
    const data = encoder.encode(components.join('|'));
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    
    return `POS_${hashHex.substring(0, 28)}`;
  }

  /**
   * Get or generate device fingerprint
   */
  private async getOrGenerateFingerprint(): Promise<string> {
    // Try to get from localStorage first
    let fingerprint = localStorage.getItem(this.STORAGE_KEY);
    
    if (!fingerprint) {
      // Generate new fingerprint
      fingerprint = await this.generateDeviceFingerprint();
    }
    
    return fingerprint;
  }

  /**
   * Get device hardware information
   */
  async getDeviceInfo(): Promise<DeviceInfo> {
    const fingerprint = await this.getOrGenerateFingerprint();
    let macAddress = '';
    
    // Try to get MAC address from Electron API if available
    if (window.erpAPI?.getMacAddress) {
      try {
        macAddress = await window.erpAPI.getMacAddress();
      } catch (error) {
        console.warn('Failed to get MAC address:', error);
        macAddress = 'unknown';
      }
    } else {
      macAddress = 'browser-mode';
    }
    
    return {
      fingerprint,
      macAddress,
      hardwareInfo: {
        platform: navigator.platform,
        arch: (navigator as any).userAgentData?.platform || 'unknown',
        userAgent: navigator.userAgent,
        screenResolution: `${screen.width}x${screen.height}`,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        language: navigator.language,
        cpuCores: navigator.hardwareConcurrency || 0,
        memory: (navigator as any).deviceMemory ? `${(navigator as any).deviceMemory}GB` : 'unknown'
      }
    };
  }

  /**
   * Check if device is activated
   */
  async checkActivationStatus(): Promise<ActivationStatus> {
    if (!this.deviceFingerprint) {
      return { activated: false, message: 'No device fingerprint' };
    }

    try {
      const deviceInfo = await this.getDeviceInfo();
      
      const response = await fetch(`${this.API_BASE_URL}/status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          device_fingerprint: this.deviceFingerprint,
          mac_address: deviceInfo.macAddress
        })
      });

      const data = await response.json();

      if (response.ok && data.success) {
        return {
          activated: true,
          deviceId: data.device_info.device_id,
          deviceName: data.device_info.device_name,
          companyName: data.device_info.company_name,
          branchName: data.device_info.branch_name,
          lastSeen: data.device_info.last_seen,
          message: 'Device is activated'
        };
      } else {
        return {
          activated: false,
          message: data.message || 'Device not activated'
        };
      }
    } catch (error) {
      console.error('Error checking activation status:', error);
      return {
        activated: false,
        message: 'Failed to check activation status'
      };
    }
  }

  /**
   * Check if MAC address is already registered
   */
  async checkMacAddress(macAddress: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.API_BASE_URL}/check-mac`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          mac_address: macAddress
        })
      });

      const data = await response.json();
      return data.registered || false;
    } catch (error) {
      console.error('Error checking MAC address:', error);
      return false;
    }
  }

  /**
   * Activate device with activation key
   */
  async activateDevice(activationKey: string): Promise<ActivationResponse> {
    try {
      const deviceInfo = await this.getDeviceInfo();

      const response = await fetch(`${this.API_BASE_URL}/activate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          activation_key: activationKey,
          device_fingerprint: deviceInfo.fingerprint,
          mac_address: deviceInfo.macAddress,
          hardware_info: deviceInfo.hardwareInfo,
          app_version: '1.0.0'
        })
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // Store fingerprint on successful activation
        this.deviceFingerprint = deviceInfo.fingerprint;
        localStorage.setItem(this.STORAGE_KEY, deviceInfo.fingerprint);
        
        return {
          success: true,
          message: data.message,
          device_info: data.device_info
        };
      } else {
        return {
          success: false,
          message: data.message || 'Activation failed'
        };
      }
    } catch (error) {
      console.error('Error activating device:', error);
      return {
        success: false,
        message: 'Network error during activation'
      };
    }
  }

  /**
   * Send heartbeat to server
   */
  async sendHeartbeat(): Promise<void> {
    if (!this.deviceFingerprint) return;

    try {
      await fetch(`${this.API_BASE_URL}/heartbeat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          device_fingerprint: this.deviceFingerprint,
          app_version: '1.0.0',
          additional_info: {
            timestamp: new Date().toISOString(),
            url: window.location.href
          }
        })
      });
    } catch (error) {
      console.error('Error sending heartbeat:', error);
    }
  }

  /**
   * Deactivate device
   */
  async deactivateDevice(reason: string = 'User requested'): Promise<boolean> {
    if (!this.deviceFingerprint) return false;

    try {
      const response = await fetch(`${this.API_BASE_URL}/deactivate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          device_fingerprint: this.deviceFingerprint,
          reason
        })
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // Clear stored fingerprint
        localStorage.removeItem(this.STORAGE_KEY);
        this.deviceFingerprint = null;
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error deactivating device:', error);
      return false;
    }
  }

  /**
   * Get device info for activation key
   */
  async getDeviceInfoByKey(activationKey: string): Promise<any> {
    try {
      const response = await fetch(`${this.API_BASE_URL}/device-info?activation_key=${activationKey}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        }
      });

      return await response.json();
    } catch (error) {
      console.error('Error getting device info:', error);
      return { success: false, message: 'Failed to get device info' };
    }
  }

  /**
   * Start heartbeat interval
   */
  startHeartbeat(intervalMs: number = 60000): void {
    setInterval(() => {
      this.sendHeartbeat();
    }, intervalMs);
  }

  /**
   * Get current device fingerprint
   */
  getFingerprint(): string | null {
    return this.deviceFingerprint;
  }

  /**
   * Check if device fingerprint is available
   */
  async checkFingerprintAvailability(fingerprint?: string): Promise<boolean> {
    const fp = fingerprint || this.deviceFingerprint;
    if (!fp) return false;

    try {
      const response = await fetch(`${this.API_BASE_URL}/check-fingerprint`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          device_fingerprint: fp
        })
      });

      const data = await response.json();
      return data.success && !data.in_use;
    } catch (error) {
      console.error('Error checking fingerprint availability:', error);
      return false;
    }
  }
}

// Export singleton instance
export const deviceService = new DeviceService();