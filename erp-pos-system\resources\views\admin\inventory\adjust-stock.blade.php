@extends('layouts.admin')

@section('title', 'Adjust Stock')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Adjust Stock - {{ $product->name }}</h3>
                    <a href="{{ route('admin.inventory.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Inventory
                    </a>
                </div>

                <form action="{{ route('admin.inventory.process-stock-adjustment', $product) }}" method="POST">
                    @csrf
                    
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="adjustment_type">Adjustment Type <span class="text-danger">*</span></label>
                                    <select name="adjustment_type" id="adjustment_type" class="form-control @error('adjustment_type') is-invalid @enderror" required>
                                        <option value="">Select Type</option>
                                        <option value="add" {{ old('adjustment_type') == 'add' ? 'selected' : '' }}>Add Stock</option>
                                        <option value="subtract" {{ old('adjustment_type') == 'subtract' ? 'selected' : '' }}>Remove Stock</option>
                                        <option value="set" {{ old('adjustment_type') == 'set' ? 'selected' : '' }}>Set Stock To</option>
                                    </select>
                                    @error('adjustment_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="quantity">Quantity <span class="text-danger">*</span></label>
                                    <input type="number" name="quantity" id="quantity" class="form-control @error('quantity') is-invalid @enderror" 
                                           value="{{ old('quantity') }}" min="0" required>
                                    @error('quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="reason">Reason <span class="text-danger">*</span></label>
                            <select name="reason" id="reason" class="form-control @error('reason') is-invalid @enderror" required>
                                <option value="">Select Reason</option>
                                <option value="Stock Count" {{ old('reason') == 'Stock Count' ? 'selected' : '' }}>Stock Count</option>
                                <option value="Damaged Goods" {{ old('reason') == 'Damaged Goods' ? 'selected' : '' }}>Damaged Goods</option>
                                <option value="Expired Items" {{ old('reason') == 'Expired Items' ? 'selected' : '' }}>Expired Items</option>
                                <option value="Theft/Loss" {{ old('reason') == 'Theft/Loss' ? 'selected' : '' }}>Theft/Loss</option>
                                <option value="Return to Supplier" {{ old('reason') == 'Return to Supplier' ? 'selected' : '' }}>Return to Supplier</option>
                                <option value="New Stock Received" {{ old('reason') == 'New Stock Received' ? 'selected' : '' }}>New Stock Received</option>
                                <option value="Correction" {{ old('reason') == 'Correction' ? 'selected' : '' }}>Correction</option>
                                <option value="Other" {{ old('reason') == 'Other' ? 'selected' : '' }}>Other</option>
                            </select>
                            @error('reason')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="notes">Additional Notes</label>
                            <textarea name="notes" id="notes" class="form-control @error('notes') is-invalid @enderror" 
                                      rows="3" placeholder="Optional additional details...">{{ old('notes') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Preview Section -->
                        <div class="card border-info mt-4" id="previewSection" style="display: none;">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">Stock Adjustment Preview</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <strong>Current Stock:</strong><br>
                                        <span class="text-muted">{{ $product->stock_quantity ?? 0 }} units</span>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>Adjustment:</strong><br>
                                        <span id="adjustmentPreview" class="text-muted">-</span>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>New Stock:</strong><br>
                                        <span id="newStockPreview" class="text-primary h5">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                            <i class="fas fa-save"></i> Apply Adjustment
                        </button>
                        <a href="{{ route('admin.inventory.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Product Info Sidebar -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Product Information</h5>
                </div>
                <div class="card-body">
                    @if($product->image_url)
                        <div class="text-center mb-3">
                            <img src="{{ $product->image_url }}" alt="{{ $product->name }}" 
                                 class="img-fluid rounded" style="max-height: 200px;">
                        </div>
                    @endif

                    <table class="table table-borderless table-sm">
                        <tr>
                            <th>Name:</th>
                            <td>{{ $product->name }}</td>
                        </tr>
                        <tr>
                            <th>SKU:</th>
                            <td>{{ $product->sku }}</td>
                        </tr>
                        <tr>
                            <th>Group:</th>
                            <td>{{ $product->group->name }}</td>
                        </tr>
                        <tr>
                            <th>Current Stock:</th>
                            <td>
                                <span class="badge badge-lg {{ $product->isOutOfStock() ? 'badge-danger' : ($product->isLowStock() ? 'badge-warning' : 'badge-success') }}">
                                    {{ $product->stock_quantity ?? 0 }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <th>Min Level:</th>
                            <td>{{ $product->min_stock_level ?? 'Not set' }}</td>
                        </tr>
                        <tr>
                            <th>Cost Price:</th>
                            <td>${{ number_format($product->cost_price ?? 0, 2) }}</td>
                        </tr>
                        <tr>
                            <th>Selling Price:</th>
                            <td>${{ number_format($product->price, 2) }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    const currentStock = {{ $product->stock_quantity ?? 0 }};
    
    function updatePreview() {
        const adjustmentType = $('#adjustment_type').val();
        const quantity = parseInt($('#quantity').val()) || 0;
        
        if (adjustmentType && quantity >= 0) {
            let newStock = currentStock;
            let adjustmentText = '';
            
            switch (adjustmentType) {
                case 'add':
                    newStock = currentStock + quantity;
                    adjustmentText = '+' + quantity;
                    break;
                case 'subtract':
                    newStock = Math.max(0, currentStock - quantity);
                    adjustmentText = '-' + quantity;
                    break;
                case 'set':
                    newStock = quantity;
                    adjustmentText = 'Set to ' + quantity;
                    break;
            }
            
            $('#adjustmentPreview').text(adjustmentText);
            $('#newStockPreview').text(newStock + ' units');
            $('#previewSection').show();
            $('#submitBtn').prop('disabled', false);
        } else {
            $('#previewSection').hide();
            $('#submitBtn').prop('disabled', true);
        }
    }
    
    $('#adjustment_type, #quantity').on('change input', updatePreview);
});
</script>
@endpush
@endsection
