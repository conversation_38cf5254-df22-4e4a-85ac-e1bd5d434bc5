@extends('layouts.admin')

@section('title', 'Add New Brand')

@section('content')
<style>
    .erp-form-label {
        font-weight: 600;
        color: #2c3e50;
        font-size: 0.85rem;
        margin-bottom: 4px;
        display: block;
    }
    .erp-form-control {
        width: 100%;
        padding: 6px 8px;
        border: 1px solid #e9ecef;
        border-radius: 5px;
        font-size: 0.85rem;
        background: #f8fafc;
        transition: border-color 0.2s, box-shadow 0.2s;
        box-shadow: none;
    }
    .erp-form-control:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 1px rgba(52,152,219,0.08);
        background: #fff;
        outline: none;
    }
    .erp-btn-primary {
        background: #007bff;
        color: #fff;
        border: none;
        border-radius: 4px;
        padding: 7px 16px;
        font-size: 0.95rem;
        font-weight: 600;
        transition: background 0.2s;
    }
    .erp-btn-primary:hover {
        background: #0056b3;
    }
    .erp-btn-outline {
        background: #fff;
        color: #007bff;
        border: 1.5px solid #007bff;
        border-radius: 4px;
        padding: 7px 16px;
        font-size: 0.95rem;
        font-weight: 600;
        transition: background 0.2s, color 0.2s;
    }
    .erp-btn-outline:hover {
        background: #007bff;
        color: #fff;
    }
</style>
<div class="container-fluid py-4" style="background: #f4f6fb; min-height: 100vh;">
    <div class="row justify-content-center">
        <div class="col-12 d-flex justify-content-center">
            <div style="max-width: 380px; width: 100%; margin-left: 32px; margin-top: 20px;">
                <h4 class="mb-0" style="font-size:1.15rem; color: #007bff; font-weight: 700;">Add New Brand</h4>
                <div>
                    @if($errors->any())
                        <div class="alert alert-danger">
                            <strong>Please fix the following errors:</strong>
                            <ul class="mb-0 mt-2">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                    <form action="{{ route('admin.brands.store') }}" method="POST" autocomplete="off">
                        @csrf
                        <div class="mb-2">
                            <label for="company_id" class="erp-form-label">Company <span class="text-danger">*</span></label>
                            <select name="company_id" id="company_id" class="erp-form-control @error('company_id') is-invalid @enderror" required>
                                <option value="">Select Company</option>
                                @foreach($companies as $company)
                                    <option value="{{ $company->id }}" {{ old('company_id') == $company->id ? 'selected' : '' }}>{{ $company->name }}</option>
                                @endforeach
                            </select>
                            @error('company_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-2">
                            <label for="name" class="erp-form-label">Brand Name <span class="text-danger">*</span></label>
                            <input type="text" name="name" id="name" class="erp-form-control @error('name') is-invalid @enderror" value="{{ old('name') }}" required autofocus style="min-width:0; max-width:100%;">
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-2">
                            <label for="description" class="erp-form-label">Description</label>
                            <textarea name="description" id="description" class="erp-form-control @error('description') is-invalid @enderror" rows="2" style="min-width:0; max-width:100%;">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="d-flex justify-content-end mt-3">
                            <a href="{{ route('admin.brands.index') }}" class="erp-btn-outline me-2">Cancel</a>
                            <button type="submit" class="erp-btn-primary">Create Brand</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
