<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use App\Http\Controllers\API\AuthController as APIAuthController;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\BrandController;
use App\Http\Controllers\BranchController;
use App\Http\Controllers\POSDeviceController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\DivisionController;
use App\Http\Controllers\GroupController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\SaleController;
use App\Http\Controllers\EndOfDayController;
use App\Http\Controllers\SyncLogController;
use App\Http\Controllers\POSLayoutController;
use App\Http\Controllers\POSLimitController;
use App\Http\Controllers\MacActivationController;
use App\Http\Controllers\PurchaseOrderController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\NotificationController;

// Handle CORS preflight requests
Route::options('{any}', function() {
    return response('', 200)
        ->header('Access-Control-Allow-Origin', '*')
        ->header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        ->header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept, X-Requested-With');
})->where('any', '.*');

// Public API endpoints
Route::get('/ping', function() {
    return response()->json(['status' => 'ok', 'message' => 'Server is running'])
        ->header('Access-Control-Allow-Origin', '*')
        ->header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        ->header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept');
});

// Company lookup endpoints (public for activation)
Route::get('/companies', function() {
    $companies = \App\Models\Company::select(['id', 'name', 'license_key', 'status'])
        ->get()
        ->map(function($company) {
            $company->license_expired = $company->license_expiry < now();
            return $company;
        });
    return response()->json(['success' => true, 'data' => $companies]);
});

Route::get('/companies/{id}', function($id) {
    try {
        $company = \App\Models\Company::with(['brands.branches'])
            ->where('id', $id)
            ->first();

        if (!$company) {
            return response()->json([
                'success' => false,
                'message' => 'Company not found'
            ], 404);
        }

        // Add license status info
        $company->license_expired = $company->license_expiry < now();

        return response()->json([
            'success' => true,
            'data' => $company
        ], 200);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error fetching company: ' . $e->getMessage()
        ], 500);
    }
});

// Get users for a company
Route::get('/companies/{id}/users', function($id) {
    try {
        $company = \App\Models\Company::find($id);

        if (!$company) {
            return response()->json([
                'success' => false,
                'message' => 'Company not found'
            ], 404);
        }

        // Get users for this company
        $users = \App\Models\User::where('company_id', $id)
            ->select(['id', 'name', 'email', 'role'])
            ->get();

        return response()->json([
            'success' => true,
            'data' => $users
        ], 200);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error fetching users: ' . $e->getMessage()
        ], 500);
    }
});

Route::post('/companies/verify', function(\Illuminate\Http\Request $request) {
    $request->validate([
        'license_key' => 'required|string'
    ]);

    $company = \App\Models\Company::where('license_key', $request->license_key)
        ->where('license_expiry', '>', now())
        ->with(['brands.branches'])
        ->first();

    if (!$company) {
        return response()->json(['success' => false, 'message' => 'Invalid license key or expired license'], 404);
    }

    return response()->json(['success' => true, 'data' => $company]);
});

// Authentication endpoints for Electron app
Route::post('/auth/login', [APIAuthController::class, 'login']);
Route::post('/auth/pin-login', [APIAuthController::class, 'pinLogin']);

// Protected API endpoints for Electron app
Route::middleware('auth:sanctum')->group(function () {
    Route::post('/auth/logout', [APIAuthController::class, 'logout']);
    Route::get('/auth/me', [APIAuthController::class, 'user']);
    Route::get('/auth/check', [APIAuthController::class, 'check']);

    // Products endpoints for POS
    Route::get('/products', [ProductController::class, 'getInventory']);
    Route::post('/sync/products', function() {
        // Simple sync endpoint - in a real implementation, this would sync from external ERP
        try {
            $user = auth()->user();
            $query = \App\Models\Product::with(['group.division.category.brand'])
                ->where('is_active', true);

            // Filter by user access if not master admin
            if ($user && !$user->isMasterAdmin()) {
                $query->whereHas('group.division.category.brand', function($q) use ($user) {
                    $q->where('company_id', $user->company_id);
                });
            }

            $products = $query->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'synced_count' => $products->count(),
                    'products' => $products
                ],
                'message' => 'Products synced successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Sync failed: ' . $e->getMessage()
            ], 500);
        }
    });

    // Sales endpoints
    Route::post('/sales', [SaleController::class, 'storePOSSale']);
    Route::get('/sales', [SaleController::class, 'index']);


    // Get screens for POS app
    Route::get('/screens', function() {
        $user = auth()->user();

        $query = \App\Models\Screen::with(['group.products' => function($q) {
            $q->where('is_active', true);
        }]);

        if ($user->branch_id) {
            // Filter by branch's brand
            $branch = \App\Models\Branch::find($user->branch_id);
            if ($branch) {
                $query->whereHas('group.division.category', function($q) use ($branch) {
                    $q->where('brand_id', $branch->brand_id);
                });
            }
        } elseif ($user->company_id) {
            // Filter by company's brands
            $brandIds = \App\Models\Brand::where('company_id', $user->company_id)->pluck('id');
            $query->whereHas('group.division.category', function($q) use ($brandIds) {
                $q->whereIn('brand_id', $brandIds);
            });
        }

        $screens = $query->where('is_active', true)
                        ->orderBy('sort_order')
                        ->get();

        return response()->json([
            'success' => true,
            'data' => $screens
        ]);
    });

    // Batch sales sync endpoint for multiple sales
    Route::post('/sales/batch', [SaleController::class, 'batchSyncSales']);

    // End of Day functionality
    Route::get('/end-of-day/sales', [EndOfDayController::class, 'getTodaysSales']);
    Route::get('/end-of-day/low-stock', [EndOfDayController::class, 'getLowStockItems']);
    Route::post('/end-of-day/sync', [EndOfDayController::class, 'syncToCloud']);
    Route::get('/end-of-day/report', [EndOfDayController::class, 'generateReport']);

    // Legacy endpoints (for backward compatibility)
    Route::get('/sales/today', [EndOfDayController::class, 'getTodaysSales']);
});

// POS Device Activation API Routes (public for device activation)
Route::prefix('pos')->group(function () {
    Route::post('/activate', [App\Http\Controllers\PosActivationController::class, 'activate']);
    Route::post('/deactivate', [App\Http\Controllers\PosActivationController::class, 'deactivate']);
    Route::post('/status', [App\Http\Controllers\PosActivationController::class, 'status']);
    Route::post('/heartbeat', [App\Http\Controllers\PosActivationController::class, 'heartbeat']);
    Route::get('/device-info', [App\Http\Controllers\PosActivationController::class, 'getDeviceInfo']);
    Route::post('/transfer', [App\Http\Controllers\PosActivationController::class, 'transfer']);
    Route::post('/check-fingerprint', [App\Http\Controllers\PosActivationController::class, 'checkFingerprint']);
    
    // New MAC address endpoints
    Route::post('/check-mac', [App\Http\Controllers\PosActivationController::class, 'checkMac']);
    Route::post('/reset-mac', [App\Http\Controllers\PosActivationController::class, 'resetMacAddress']);
});

// MAC Address Activation endpoints (public for device activation)
Route::prefix('mac-activation')->group(function() {
    Route::post('/activate-device', [MacActivationController::class, 'activateDevice']);
    Route::post('/check-activation', [MacActivationController::class, 'checkMacActivation']);
});

// Master Admin MAC Management endpoints (protected)
Route::middleware(['web', 'auth'])->prefix('admin/mac-management')->group(function() {
    Route::post('/reset-mac', [MacActivationController::class, 'resetMacAddress']);
    Route::get('/company/{companyId}/macs', [MacActivationController::class, 'getCompanyMacAddresses']);
    Route::get('/all-companies-macs', [MacActivationController::class, 'getAllCompaniesWithMacs']);
});

// Company devices endpoint
Route::get('/companies/{id}/devices', [App\Http\Controllers\PosActivationController::class, 'getCompanyDevices']);