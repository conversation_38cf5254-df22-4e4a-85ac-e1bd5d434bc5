import React, { useState, useEffect } from 'react';
import './ActivationPage.css';
import { useMacDeviceActivation } from '../hooks/useMacDeviceActivation';

interface ActivationPageProps {
  onActivationComplete: (serverUrl: string, companyData: any) => void;
}

const ActivationPage: React.FC<ActivationPageProps> = ({ onActivationComplete }) => {
  const { error: activationError, activateDevice, macAddress } = useMacDeviceActivation();
  
  const [serverUrl, setServerUrl] = useState('http://127.0.0.1:8000');
  const [companyId, setCompanyId] = useState('');
  const [activationKey, setActivationKey] = useState('');
  const [companyInfo, setCompanyInfo] = useState<any>(null);
  const [isActivating, setIsActivating] = useState(false);
  const [isLoadingCompany, setIsLoadingCompany] = useState(false);
  const [error, setError] = useState('');
  const [step, setStep] = useState<'server' | 'company' | 'activation' | 'complete'>('server');
  const [activationSteps, setActivationSteps] = useState([
    { id: 1, title: 'Server Connection', description: 'Connect to ERP server', completed: false },
    { id: 2, title: 'Company Validation', description: 'Validate Company ID and License', completed: false },
    { id: 3, title: 'Device Registration', description: 'Register device with MAC address', completed: false },
    { id: 4, title: 'Data Sync', description: 'Download company data and users', completed: false }
  ]);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);

  // Check for existing one-time activation on component mount
  useEffect(() => {
    const checkExistingActivation = () => {
      const existingActivation = localStorage.getItem('device_activation');
      const deviceFingerprint = localStorage.getItem('device_fingerprint');

      if (existingActivation && deviceFingerprint) {
        try {
          const activation = JSON.parse(existingActivation);
          const fingerprint = JSON.parse(deviceFingerprint);

          // Verify activation integrity
          if (activation.isActivated &&
              activation.macAddress === macAddress &&
              fingerprint.macAddress === macAddress &&
              activation.activatedAt) {

            // Device is already activated - show message and redirect
            setStep('complete');
            setError('');

            // Set all steps as completed
            setActivationSteps(prev => prev.map(step => ({ ...step, completed: true })));

            // Show completion message
            setTimeout(() => {
              alert('✅ This device is already activated!\n\nDevice MAC: ' + macAddress + '\nCompany: ' + (activation.companyInfo?.name || 'Unknown') + '\nActivated: ' + new Date(activation.activatedAt).toLocaleString() + '\n\nRedirecting to login...');
              onActivationComplete(activation.companyInfo?.server_url || serverUrl, activation.companyInfo);
            }, 1000);

            return;
          }
        } catch (error) {
          console.error('Error checking existing activation:', error);
          // Clear corrupted activation data
          localStorage.removeItem('device_activation');
          localStorage.removeItem('device_fingerprint');
        }
      }

      // Load existing server URL if available
      const storedServerUrl = localStorage.getItem('erp_server_url');
      if (storedServerUrl) {
        setServerUrl(storedServerUrl);
      }
    };

    // Only check if MAC address is available
    if (macAddress) {
      checkExistingActivation();
    }
  }, [macAddress, onActivationComplete, serverUrl]);

  const updateStepStatus = (stepId: number, completed: boolean) => {
    setActivationSteps(prev =>
      prev.map(step =>
        step.id === stepId ? { ...step, completed } : step
      )
    );
  };

  const testServerConnection = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsActivating(true);
    setError('');
    setCurrentStepIndex(0);

    try {
      // Step 1: Test connection to the server
      const response = await fetch(`${serverUrl}/api/ping`);

      if (!response.ok) {
        throw new Error('Cannot connect to ERP server');
      }

      const data = await response.json();

      if (data.status === 'ok') {
        updateStepStatus(1, true);
        setCurrentStepIndex(1);
        setStep('company');
      } else {
        throw new Error('Invalid server response');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Server connection failed');
    } finally {
      setIsActivating(false);
    }
  };

  const fetchCompanyInfo = async (id: string) => {
    if (!id.trim()) {
      setCompanyInfo(null);
      return;
    }

    setIsLoadingCompany(true);
    setError('');

    try {
      const response = await fetch(`${serverUrl}/api/companies/${id}`);
      const data = await response.json();

      if (response.ok && (data.id || (data.success && data.data))) {
        const companyData = data.data || data;
        setCompanyInfo(companyData);
        setError('');
      } else {
        setCompanyInfo(null);
        setError(data.message || 'Company not found');
      }
    } catch (err) {
      console.error('Error fetching company info:', err);
      setCompanyInfo(null);
      setError(err instanceof Error ? err.message : 'Failed to fetch company information');
    } finally {
      setIsLoadingCompany(false);
    }
  };

  const handleCompanyIdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const id = e.target.value;
    setCompanyId(id);

    // Clear previous company info and errors
    setCompanyInfo(null);
    setError('');

    // Auto-fetch company info when ID is entered
    if (id.trim()) {
      setTimeout(() => {
        fetchCompanyInfo(id);
      }, 800);
    }
  };

  const handleActivateDevice = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!companyId.trim()) {
      setError('Please enter a company ID');
      return;
    }

    if (!activationKey.trim()) {
      setError('Please enter the activation key');
      return;
    }

    if (!companyInfo) {
      setError('Please enter a valid company ID');
      return;
    }

    setIsActivating(true);
    setError('');
    setStep('activation');
    setCurrentStepIndex(1);

    try {
      // Step 2: Company Validation
      updateStepStatus(2, true);
      setCurrentStepIndex(2);
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Step 3: Device Registration with MAC address
      const success = await activateDevice(companyId, activationKey);
elecgt
      if (success) {
        updateStepStatus(4, true);
        // Step 4: Data Synchronization
        updateStepStatus(4, true);
        setCurrentStepIndex(4);
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Step 4: Store one-time activation data
        // Step 4: Store one-time activation data
        const activationData = {
          isActivated: true,
          macAddress: macAddress,
          companyId: companyId,
          activatedAt: new Date().toISOString(),
          // Remove sensitive token from localStorage
          companyInfo: companyInfo
        };

        localStorage.setItem('device_activation', JSON.stringify(activationData));
        // Consider storing device fingerprint in a more secure way
        localStorage.setItem('erp_server_url', serverUrl);
        localStorage.setItem('app_activated', 'true');
        }));
        localStorage.setItem('erp_server_url', serverUrl);
        localStorage.setItem('app_activated', 'true');

        // Step 4: Data Synchronization
        updateStepStatus(4, true);
        setCurrentStepIndex(3);
        await new Promise(resolve => setTimeout(resolve, 1500));

        setStep('complete');

        // Complete activation after a short delay
        setTimeout(() => {
          onActivationComplete(serverUrl, companyInfo);
        }, 2000);
      } else {
        setError(activationError || 'Activation failed');
        
        // Check if error is related to MAC address already registered
        if (activationError?.toLowerCase().includes('mac') || activationError?.toLowerCase().includes('already registered')) {
          setError(`${activationError}. Please contact administrator to reset device activation.`);
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Activation failed');
    } finally {
      setIsActivating(false);
    }
  };

  return (
    <div className="activation-container">
      <div className="activation-wrapper">
        {/* Main Card */}
        <div className="activation-card">
          {/* Header */}
          <div className="activation-header">
            <div className="activation-icon-container">
              <div className="activation-icon"></div>
            </div>
            <h1 className="activation-title">
              {step === 'activation' ? '🔐 One-Time Device Activation' : 'ERP POS Setup'}
            </h1>
            <p className="activation-subtitle">
              {step === 'server' && 'Connect to your ERP server'}
              {step === 'company' && 'Enter company information'}
              {step === 'activation' && 'Activating device with your company credentials'}
              {step === 'complete' && '✅ Activation completed successfully!'}
            </p>
            {macAddress && (
              <div className="device-info">
                <strong>Device MAC Address:</strong> {macAddress}
              </div>
            )}
          </div>

          {/* Activation Steps Progress */}
          {(step === 'activation' || step === 'complete') && (
            <div className="activation-steps">
              <h3>Activation Progress</h3>
              <div className="steps-container">
                {activationSteps.map((stepItem, index) => (
                  <div
                    key={stepItem.id}
                    className={`step ${stepItem.completed ? 'completed' : ''} ${currentStepIndex === index ? 'active' : ''}`}
                  >
                    <div className="step-number">
                      {stepItem.completed ? '✓' : stepItem.id}
                    </div>
                    <div className="step-content">
                      <div className="step-title">{stepItem.title}</div>
                      <div className="step-description">{stepItem.description}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Server Step */}
          {step === 'server' && (
            <form onSubmit={testServerConnection} className="activation-form">
              <div className="activation-form-group">
                <label className="activation-label">
                  ERP Server URL
                </label>
                <input
                  type="url"
                  value={serverUrl}
                  onChange={(e) => setServerUrl(e.target.value)}
                  placeholder="http://127.0.0.1:8000"
                  required
                  className="activation-input"
                />
              </div>

              {error && (
                <div className="activation-error">
                  {error}
                </div>
              )}

              <button
                type="submit"
                disabled={isActivating}
                className={`activation-button activation-button-primary ${isActivating ? 'activation-button-disabled' : ''}`}
              >
                {isActivating ? (
                  <>
                    <div className="activation-spinner"></div>
                    Connecting...
                  </>
                ) : (
                  'Connect to Server'
                )}
              </button>
            </form>
          )}

          {/* Company Step */}
          {step === 'company' && (
            <div className="activation-form">
              <form onSubmit={handleActivateDevice} className="activation-form">
                <div className="activation-form-group">
                  <label className="activation-label">Company ID</label>
                  <input
                    type="number"
                    value={companyId}
                    onChange={handleCompanyIdChange}
                    placeholder="Enter company ID"
                    required
                    min="1"
                    className="activation-input"
                  />
                  {isLoadingCompany && (
                    <p className="text-indigo-200 text-sm mt-2">Loading company information...</p>
                  )}
                </div>

                <div className="activation-form-group">
                  <label className="activation-label">Activation Key</label>
                  <input
                    type="text"
                    value={activationKey}
                    onChange={(e) => setActivationKey(e.target.value)}
                    placeholder="Enter activation key (e.g., ABC-LICENSE-2024)"
                    required
                    className="activation-input"
                  />
                  <p className="text-indigo-200 text-xs mt-1">
                    Enter the license key provided by your administrator
                  </p>
                </div>

                {/* Device Information */}
                {macAddress && (
                  <div className="bg-blue-500/20 border border-blue-500/30 rounded-xl p-3 text-sm text-blue-200 mb-4">
                    <p><strong>Device MAC Address:</strong> {macAddress}</p>
                    <p className="text-xs mt-1">
                      This device will be bound to company ID {companyId}.
                      Contact administrator to reset if needed.
                    </p>
                  </div>
                )}

                {companyInfo && (
                  <div className="bg-emerald-500/20 border border-emerald-500/30 rounded-xl p-4 backdrop-blur-sm">
                    <h4 className="font-medium text-white mb-2">Company Found:</h4>
                    <div className="text-sm text-white/90 space-y-1">
                      <p><strong>Name:</strong> {companyInfo.name}</p>
                      {companyInfo.license_key && (
                        <p><strong>License Key:</strong> {companyInfo.license_key}</p>
                      )}
                      {companyInfo.status && (
                        <p><strong>Status:</strong> {companyInfo.status}</p>
                      )}
                    </div>
                  </div>
                )}

                <button
                  type="submit"
                  disabled={isActivating || !companyInfo || !companyId.trim() || !activationKey.trim()}
                  className="w-full py-3 px-4 bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700 text-white font-semibold rounded-xl shadow-lg transition transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed mt-4"
                >
                  {isActivating ? 'Activating...' : 'Activate POS Device'}
                </button>
              </form>

              {error && (
                <div className="bg-red-500/20 border border-red-500/30 rounded-xl p-3 text-sm text-red-200 mt-4">
                  {error}
                </div>
              )}

              {!localStorage.getItem('device_activation') && (
                <button
                  onClick={() => setStep('server')}
                  className="w-full text-white/60 hover:text-white text-sm mt-4"
                >
                  ← Back to Server Settings
                </button>
              )}
            </div>
          )}

          {/* Activation in Progress */}
          {step === 'activation' && (
            <div className="activation-progress">
              <div className="progress-spinner"></div>
              <p>Activating device... Please wait</p>
              <small>This is a one-time process and may take a few moments</small>
            </div>
          )}

          {/* Activation Complete */}
          {step === 'complete' && (
            <div className="activation-complete">
              <div className="completion-message">
                <div className="success-icon">✅</div>
                <h3>
                  {localStorage.getItem('device_activation') ?
                    'Device Already Activated!' :
                    'Device Activated Successfully!'
                  }
                </h3>
                <p>
                  {localStorage.getItem('device_activation') ?
                    'This device was previously activated and is permanently linked to your company' :
                    `Your POS device has been permanently linked to ${companyInfo?.name}`
                  }
                </p>
                <div className="completion-details">
                  <p><strong>Company:</strong> {companyInfo?.name || 'Loading...'}</p>
                  <p><strong>Device MAC:</strong> {macAddress}</p>
                  <p><strong>Activated:</strong> {
                    localStorage.getItem('device_activation') ?
                      (() => {
                        try {
                          const activation = JSON.parse(localStorage.getItem('device_activation') || '{}');
                          return new Date(activation.activatedAt).toLocaleString();
                        } catch {
                          return 'Unknown';
                        }
                      })() :
                      new Date().toLocaleString()
                  }</p>
                </div>
              </div>

              <div className="activation-info">
                <h4>📋 Important Information:</h4>
                <ul>
                  <li>✅ This activation is permanent and one-time only</li>
                  <li>🔒 Your device is now securely linked to your company</li>
                  <li>🔄 Future app launches will skip this activation step</li>
                  <li>💾 All activation data is stored locally for offline use</li>
                  {localStorage.getItem('device_activation') && (
                    <li>⚠️ Reactivation is not possible without admin reset</li>
                  )}
                </ul>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="text-center mt-6">
          <p className="text-white/40 text-xs">ERP POS System © 2024</p>
        </div>
      </div>
    </div>
  );
};

export default ActivationPage;
