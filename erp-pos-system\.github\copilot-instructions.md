# Copilot Instructions for ERP POS System

## Project Overview
- This is a Laravel-based ERP Point-of-Sale (POS) system.
- The codebase follows standard Laravel conventions for MVC structure, routing, and configuration.
- Major components are organized under `app/` (Controllers, Models, Middleware), `routes/`, `resources/views/`, and `database/` (migrations, seeders, factories).

## Key Architectural Patterns
- **Models**: Located in `app/Models/`, each represents a database table (e.g., `Product`, `Sale`, `User`).
- **Controllers**: In `app/Http/Controllers/`, handle HTTP requests and business logic.
- **Routes**: Defined in `routes/web.php` (web) and `routes/api.php` (API endpoints).
- **Migrations**: In `database/migrations/`, define schema for all entities (e.g., `products`, `sales`, `users`).
- **Views**: Blade templates in `resources/views/` (not shown above, but standard for Laravel).

## Developer Workflows
- **Start local server**: `php artisan serve`
- **Run migrations**: `php artisan migrate`
- **Seed database**: `php artisan db:seed`
- **Run tests**: `php artisan test` or `vendor\bin\phpunit`
- **Install dependencies**: `composer install` (PHP), `npm install` (JS/CSS)
- **Build frontend assets**: `npm run build` (uses Vite)

## Project-Specific Conventions
- **Model Naming**: Singular, PascalCase (e.g., `Product`, `SaleItem`).
- **Migration Filenames**: Timestamped, descriptive (e.g., `2025_07_20_001552_create_products_table.php`).
- **API Structure**: RESTful, with endpoints grouped by resource in `routes/api.php`.
- **Service Providers**: Registered in `app/Providers/` and configured in `config/app.php`.

## Integration Points
- **External Packages**: Managed via Composer (`composer.json`).
- **Frontend**: Uses Vite for asset bundling (`vite.config.js`).
- **Testing**: Uses PHPUnit (`phpunit.xml`, tests in `tests/`).

## Examples
- To add a new entity:
  1. Create a model in `app/Models/`.
  2. Add a migration in `database/migrations/`.
  3. Register routes in `routes/api.php` or `routes/web.php`.
  4. Implement controller logic in `app/Http/Controllers/`.

## References
- See `README.md` for general Laravel info.
- Key files: `artisan`, `composer.json`, `vite.config.js`, `phpunit.xml`, `routes/`, `app/Models/`, `app/Http/Controllers/`, `database/migrations/`.

---
If any conventions or workflows are unclear, please request clarification or examples from the user.
