#!/bin/bash

# Deploy React POS App to Laravel Backend
echo "🚀 Deploying React POS App to Laravel Backend..."

# Build the React app
echo "📦 Building React application..."
npm run build

# Check if build was successful
if [ $? -ne 0 ]; then
    echo "❌ Build failed! Please fix the errors and try again."
    exit 1
fi

# Copy built files to Laravel public directory
echo "📁 Copying built files to Laravel backend..."

# Create POS directory in Laravel public folder
mkdir -p ../erp-pos-system/public/pos

# Copy all built files
cp -r dist/* ../erp-pos-system/public/pos/

# Update the index.html to use correct asset paths
echo "🔧 Updating asset paths..."
sed -i 's|/assets/|/pos/assets/|g' ../erp-pos-system/public/pos/index.html

echo "✅ Deployment completed successfully!"
echo "🌐 POS App is now available at: /pos"
echo "📱 Access it from your Laravel application at: http://your-domain.com/pos"
