
@extends('layouts.admin')

@section('title', 'Divisions Management - ERP System')

@section('page-title', 'Divisions Management')

@section('breadcrumb', 'Home > Admin > Divisions')

@section('styles')
<style>
    .division-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border-left: 5px solid #667eea;
        margin-bottom: 20px;
    }
    .division-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }
    .division-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    .division-name {
        font-size: 20px;
        font-weight: 700;
        color: #2d3748;
        margin: 0;
    }
    .division-category {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 6px 12px;
        border-radius: 8px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    .division-description {
        color: #718096;
        margin-bottom: 20px;
        line-height: 1.6;
    }
    .division-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
    }
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    .status-active {
        background: #c6f6d5;
        color: #22543d;
    }
    .status-inactive {
        background: #fed7d7;
        color: #742a2a;
    }
    .header-actions {
        display: flex;
        gap: 15px;
        margin-bottom: 30px;
        align-items: center;
    }
    .search-filters {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        margin-bottom: 30px;
    }
    .filter-row {
        display: grid;
        grid-template-columns: 1fr 1fr auto;
        gap: 20px;
        align-items: end;
    }
    .filter-group {
        display: flex;
        flex-direction: column;
    }
    .filter-group label {
        font-weight: 600;
        color: #2d3748;
    }
    .filter-group select {
        width: 100%;
        padding: 10px;
        border-radius: 8px;
        border: 2px solid #e2e8f0;
        font-size: 14px;
        margin-top: 5px;
    }
</style>
@endsection

@section('content')
<div class="header-actions">
    <a href="{{ route('admin.divisions.create') }}" class="btn btn-primary">
        <i style="margin-right: 8px;">＋</i> Create Division
    </a>
</div>

<div class="search-filters">
    <form method="GET" action="{{ route('admin.divisions.index') }}">
        <div class="filter-row">
            <div class="filter-group">
                <label for="category_id">Filter by Category</label>
                <select id="category_id" name="category_id">
                    <option value="">All Categories</option>
                    @foreach($categories as $category)
                        <option value="{{ $category->id }}" {{ request('category_id') == $category->id ? 'selected' : '' }}>
                            {{ $category->name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="filter-group">
                <label for="status">Status</label>
                <select id="status" name="status">
                    <option value="">All Status</option>
                    <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>Active</option>
                    <option value="0" {{ request('status') == '0' ? 'selected' : '' }}>Inactive</option>
                </select>
            </div>
            <button type="submit" class="btn btn-secondary">Filter</button>
        </div>
    </form>
</div>

@foreach($divisions as $division)
    <div class="division-card">
        <div class="division-header">
            <h3 class="division-name">{{ $division->name }}</h3>
            <span class="division-category">{{ $division->category->name ?? 'N/A' }}</span>
        </div>
        <div class="division-description">
            {{ $division->description ?? 'No description.' }}
        </div>
        <div>
            <span class="status-badge {{ $division->is_active ? 'status-active' : 'status-inactive' }}">
                {{ $division->is_active ? 'Active' : 'Inactive' }}
            </span>
        </div>
        <div class="division-actions">
            <a href="{{ route('admin.divisions.edit', $division->id) }}" class="btn btn-sm btn-primary">Edit</a>
            <a href="{{ route('admin.divisions.show', $division->id) }}" class="btn btn-sm btn-secondary">View</a>
        </div>
    </div>
@endforeach

@if($divisions->isEmpty())
    <div style="text-align:center; color:#718096; margin-top:40px;">
        No divisions found.
    </div>
@endif
@endsection