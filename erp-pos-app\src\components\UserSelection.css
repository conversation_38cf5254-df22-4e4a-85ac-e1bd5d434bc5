/* UserSelection Component Styles */

.user-selection-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #e2d1f9 0%, #bfdbfe 50%, #fce7f3 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
}

.loading-text {
  color: white;
  font-size: 20px;
}

.user-selection-card {
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(16px);
  border-radius: 16px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  padding: 32px;
  width: 100%;
  max-width: 448px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.live-badge-container {
  text-align: center;
  margin-bottom: 32px;
}

.live-badge {
  display: inline-flex;
  align-items: center;
  background: #bbf7d0;
  padding: 8px 16px;
  border-radius: 9999px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.live-indicator {
  width: 12px;
  height: 12px;
  background: white;
  border-radius: 50%;
  margin-right: 8px;
}

.live-text {
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.05em;
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.pin-input-section {
  display: flex;
  flex-direction: column;
}

.pin-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 12px;
  text-align: center;
}

.pin-display {
  width: 100%;
  padding: 24px 16px;
  border: 2px solid #d1d5db;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60px;
}

.pin-dots {
  display: flex;
  gap: 16px;
}

.pin-dot {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid;
  transition: all 0.2s ease;
}

.pin-dot-filled {
  background: #2563eb;
  border-color: #2563eb;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.pin-dot-empty {
  background: white;
  border-color: #9ca3af;
}

.keypad {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.keypad-button {
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 18px;
  font-weight: 500;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.15s ease;
  cursor: pointer;
  border: none;
}

.keypad-button:active {
  transform: scale(0.95);
}

.keypad-button-number {
  background: rgba(255, 255, 255, 0.8);
  color: #1f2937;
  border: 1px solid #e5e7eb;
}

.keypad-button-number:hover {
  background: white;
}

.keypad-button-enter {
  background: #93c5fd;
  color: #1f2937;
}

.keypad-button-enter:hover {
  background: #60a5fa;
}

.keypad-button-clear {
  background: #fca5a5;
  color: #1f2937;
}

.keypad-button-clear:hover {
  background: #f87171;
}

.keypad-button-disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.keypad-button-disabled:hover {
  background: inherit;
  transform: none;
}

.error-message {
  background: #fee2e2;
  border: 1px solid #fecaca;
  color: #b91c1c;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
}

.back-link-container {
  text-align: center;
}

.back-link {
  color: #374151;
  text-decoration: underline;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
}

.back-link:hover {
  color: #111827;
}

.loading-container {
  text-align: center;
  padding: 16px 0;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 2px solid transparent;
  border-bottom: 2px solid #93c5fd;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

.loading-message {
  margin-top: 8px;
  color: #374151;
}

.instructions {
  margin-top: 32px;
  font-size: 12px;
  color: #6b7280;
  text-align: center;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 480px) {
  .user-selection-card {
    padding: 24px;
    margin: 16px;
  }
  
  .keypad {
    gap: 16px;
  }
  
  .keypad-button {
    width: 56px;
    height: 56px;
    font-size: 16px;
  }
  
  .pin-dots {
    gap: 12px;
  }
  
  .pin-dot {
    width: 16px;
    height: 16px;
  }
}
