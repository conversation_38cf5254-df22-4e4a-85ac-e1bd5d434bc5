/**
 * React Hook for managing POS device activation
 */

import { useState, useEffect, useCallback } from 'react';
import { deviceService } from '../services/deviceService';
import type { ActivationStatus } from '../services/deviceService';

export interface UseDeviceActivationReturn {
  isActivated: boolean;
  isLoading: boolean;
  deviceInfo: ActivationStatus | null;
  error: string | null;
  activateDevice: (activationKey: string) => Promise<boolean>;
  deactivateDevice: (reason?: string) => Promise<boolean>;
  checkStatus: () => Promise<void>;
  refreshStatus: () => Promise<void>;
}

export const useDeviceActivation = (): UseDeviceActivationReturn => {
  const [isActivated, setIsActivated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [deviceInfo, setDeviceInfo] = useState<ActivationStatus | null>(null);
  const [error, setError] = useState<string | null>(null);

  /**
   * Check device activation status
   */
  const checkStatus = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const status = await deviceService.initialize();
      
      setIsActivated(status.activated);
      setDeviceInfo(status);
      
      if (!status.activated && status.message) {
        setError(status.message);
      }
      
      // Start heartbeat if activated
      if (status.activated) {
        deviceService.startHeartbeat(60000); // Every minute
      }
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      setIsActivated(false);
      setDeviceInfo(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Refresh activation status
   */
  const refreshStatus = useCallback(async () => {
    await checkStatus();
  }, [checkStatus]);

  /**
   * Activate device with activation key
   */
  const activateDevice = useCallback(async (activationKey: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);
      
      const result = await deviceService.activateDevice(activationKey);
      
      if (result.success) {
        // Refresh status after successful activation
        await checkStatus();
        return true;
      } else {
        setError(result.message);
        return false;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Activation failed';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [checkStatus]);

  /**
   * Deactivate device
   */
  const deactivateDevice = useCallback(async (reason: string = 'User requested'): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);
      
      const success = await deviceService.deactivateDevice(reason);
      
      if (success) {
        setIsActivated(false);
        setDeviceInfo(null);
        return true;
      } else {
        setError('Failed to deactivate device');
        return false;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Deactivation failed';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Initialize on mount
   */
  useEffect(() => {
    checkStatus();
  }, [checkStatus]);

  /**
   * Auto-refresh status every 5 minutes
   */
  useEffect(() => {
    const interval = setInterval(() => {
      if (isActivated) {
        refreshStatus();
      }
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [isActivated, refreshStatus]);

  return {
    isActivated,
    isLoading,
    deviceInfo,
    error,
    activateDevice,
    deactivateDevice,
    checkStatus,
    refreshStatus
  };
};