import React, { useState, useEffect } from 'react';
import './UserSelection.css';

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
}

interface UserSelectionProps {
  onLogin: (user: User, token: string) => void;
  onBackToActivation: () => void;
}

const UserSelection: React.FC<UserSelectionProps> = ({ onLogin, onBackToActivation }) => {
  const [pin, setPin] = useState('');
  const [, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLogging, setIsLogging] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      let serverUrl = localStorage.getItem('erp_server_url');
      let companyData = JSON.parse(localStorage.getItem('company_data') || '{}');

      // If not found in separate storage, try to get from device activation
      if (!serverUrl || !companyData.id) {
        const deviceActivation = localStorage.getItem('device_activation');
        if (deviceActivation) {
          try {
            const activation = JSON.parse(deviceActivation);
            if (activation.companyInfo) {
              companyData = activation.companyInfo;
              serverUrl = activation.companyInfo.server_url || serverUrl;
            }
          } catch (error) {
            console.error('Error parsing device activation:', error);
          }
        }
      }

      if (!serverUrl || !companyData.id) {
        throw new Error('Server URL or company data not found');
      }

      const response = await fetch(`${serverUrl}/api/companies/${companyData.id}/users`, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && Array.isArray(data.data)) {
          setUsers(data.data);
          console.log('Loaded users from ERP:', data.data);
        } else {
          throw new Error('Invalid response format');
        }
      } else {
        throw new Error(`Failed to load users: ${response.status}`);
      }
    } catch (error) {
      console.error('Failed to load users:', error);
      setError('Failed to load users from server. Please check connection.');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePinInput = (value: string) => {
    if (value === 'Cl') {
      setPin('');
      setError('');
      return;
    }
    if (value === 'Enter') {
      handleLogin();
      return;
    }
    if (pin.length < 6) {
      setPin(prev => prev + value);
    }
  };

  const handleLogin = async () => {
    if (pin.length === 0) return;

    setIsLogging(true);
    setError('');

    try {
      const serverUrl = localStorage.getItem('erp_server_url');

      if (!serverUrl) {
        throw new Error('Server URL not configured');
      }

      // Authenticate with PIN/password
      const response = await fetch(`${serverUrl}/api/auth/pin-login`, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pin: pin,
          company_id: JSON.parse(localStorage.getItem('company_data') || '{}').id
        }),
      });

      if (response.ok) {
        const data = await response.json();

        if (data.success && data.user && data.token) {
          // Store authentication data
          localStorage.setItem('auth_token', data.token);
          localStorage.setItem('user_data', JSON.stringify(data.user));
          localStorage.setItem('logged_in', 'true');

          onLogin(data.user, data.token);
        } else {
          setError(data.message || 'Invalid PIN');
        }
      } else {
        const errorData = await response.json().catch(() => ({}));
        setError(errorData.message || 'Authentication failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('Connection failed. Please check your network.');
    } finally {
      setIsLogging(false);
    }
  };

  const pinButtons = [
    ['1', '2', '3'],
    ['4', '5', '6'],
    ['7', '8', '9'],
    ['Cl', '0', 'Enter']
  ];

  if (isLoading) {
    return (
      <div className="user-selection-container">
        <div className="loading-text">Loading...</div>
      </div>
    );
  }

  return (
    <div className="user-selection-container">
      <div className="user-selection-card">

        {/* Live Badge */}
        <div className="live-badge-container">
          <div className="live-badge">
            <span className="live-indicator"></span>
            <span className="live-text">LIVE</span>
          </div>
        </div>

        <div className="form-content">

          {/* PIN Input */}
          <div className="pin-input-section">
            <label className="pin-label">
              Enter your Code:
            </label>
            <div className="pin-display">
              <div className="pin-dots">
                {[0, 1, 2, 3, 4, 5].map((index) => (
                  <div
                    key={index}
                    className={`pin-dot ${index < pin.length ? 'pin-dot-filled' : 'pin-dot-empty'}`}
                  />
                ))}
              </div>
            </div>
          </div>

          {/* Keypad */}
          <div className="keypad">
            {pinButtons.flat().map((button, index) => (
              <button
                key={index}
                type="button"
                onClick={() => handlePinInput(button)}
                disabled={isLogging}
                className={`keypad-button ${
                  button === 'Enter' ? 'keypad-button-enter' :
                  button === 'Cl' ? 'keypad-button-clear' :
                  'keypad-button-number'
                } ${isLogging ? 'keypad-button-disabled' : ''}`}
              >
                {button}
              </button>
            ))}
          </div>

          {/* Error */}
          {error && (
            <div className="error-message">
              {error}
            </div>
          )}

          {/* Back Link */}
          <div className="back-link-container">
            <button
              type="button"
              onClick={onBackToActivation}
              className="back-link"
            >
              Back to Activation
            </button>
          </div>

          {/* Loading */}
          {isLogging && (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <p className="loading-message">Logging in...</p>
            </div>
          )}
        </div>

        {/* Instructions */}
        <div className="instructions">
          <p>Enter your assigned PIN code to access the system</p>
        </div>
      </div>
    </div>
  );
};

export default UserSelection;
