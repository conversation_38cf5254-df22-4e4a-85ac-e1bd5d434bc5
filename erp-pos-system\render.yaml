services:
  - type: web
    name: erp-backend
    runtime: docker
    plan: free
    envVars:
      - key: APP_KEY
        generateValue: true
      - key: APP_ENV
        value: production
      - key: APP_DEBUG
        value: false
      - key: APP_URL
        value: https://erp-pos-system.onrender.com
      - key: DB_CONNECTION
        value: pgsql
      - key: DB_HOST
        fromDatabase:
          name: erpdb
          property: host
      - key: DB_PORT
        fromDatabase:
          name: erpdb
          property: port
      - key: DB_DATABASE
        fromDatabase:
          name: erpdb
          property: database
      - key: DB_USERNAME
        fromDatabase:
          name: erpdb
          property: user
      - key: DB_PASSWORD
        fromDatabase:
          name: erpdb
          property: password
      - key: SESSION_DRIVER
        value: database
      - key: CACHE_DRIVER
        value: database
      - key: QUEUE_CONNECTION
        value: database
      - key: LOG_CHANNEL
        value: stderr
      - key: LOG_LEVEL
        value: info
      - key: MAIL_MAILER
        value: smtp
      - key: MAIL_HOST
        value: smtp.gmail.com
      - key: MAIL_PORT
        value: 587
      - key: MAIL_USERNAME
        value: <EMAIL>
      - key: MAIL_PASSWORD
        value: your-app-password
      - key: MAIL_ENCRYPTION
        value: tls
      - key: MAIL_FROM_ADDRESS
        value: <EMAIL>
      - key: MAIL_FROM_NAME
        value: ERP POS System

databases:
  - name: erpdb
    plan: free
