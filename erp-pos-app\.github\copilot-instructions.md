<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

This project is a modular Electron.js + React.js POS system with Tailwind CSS, SQLite, authentication, API sync, license key validation, multi-user roles, theme toggle, and inventory updates. Follow best practices for clean, scalable code.
