﻿// SyncManager.tsx - Component to manage ERP synchronization
import { useState } from 'react';

interface SyncResult {
  success?: boolean;
  connected?: boolean;
  count?: number;
  total?: number;
  error?: string;
  data?: any;
}

export default function SyncManager() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<SyncResult | null>(null);

  const handleLogin = async () => {
    setIsLoading(true);
    try {
      const loginResult = await window.electron.erp.login(username, password);
      setResult(loginResult);
      setIsLoggedIn(loginResult.success || false);
    } catch (error) {
      setResult({ success: false, error: 'Login failed' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestConnection = async () => {
    setIsLoading(true);
    try {
      const testResult = await window.electron.erp.testConnection();
      setResult(testResult);
    } catch (error) {
      setResult({ connected: false, error: 'Connection test failed' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSyncInventory = async () => {
    setIsLoading(true);
    try {
      const syncResult = await window.electron.erp.syncInventory();
      setResult(syncResult);
    } catch (error) {
      setResult({ success: false, error: 'Inventory sync failed' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSyncSales = async () => {
    setIsLoading(true);
    try {
      const syncResult = await window.electron.erp.syncSales();
      setResult(syncResult);
    } catch (error) {
      setResult({ success: false, error: 'Sales sync failed' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-xl mx-auto bg-white rounded-xl shadow-md">
      <h2 className="text-xl font-bold mb-4">ERP Sync Manager</h2>
      
      {!isLoggedIn ? (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Username</label>
            <input
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700">Password</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
          
          <button
            onClick={handleLogin}
            disabled={isLoading}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {isLoading ? 'Logging in...' : 'Login to ERP'}
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex space-x-2">
            <button
              onClick={handleTestConnection}
              disabled={isLoading}
              className="flex-1 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
            >
              Test Connection
            </button>
            
            <button
              onClick={handleSyncInventory}
              disabled={isLoading}
              className="flex-1 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              Sync Inventory
            </button>
            
            <button
              onClick={handleSyncSales}
              disabled={isLoading}
              className="flex-1 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"
            >
              Sync Sales
            </button>
          </div>
          
          <button
            onClick={() => setIsLoggedIn(false)}
            className="w-full py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Logout
          </button>
        </div>
      )}
      
      {result && (
        <div className="mt-4 p-4 border rounded-md">
          <h3 className="text-lg font-medium mb-2">Result:</h3>
          <pre className="bg-gray-100 p-2 rounded overflow-auto max-h-40">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
