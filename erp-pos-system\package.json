{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@tailwindcss/vite": "^4.0.0", "axios": "^1.10.0", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "tailwindcss": "^4.0.0", "vite": "^6.2.4"}, "dependencies": {"electron": "^37.2.3", "electron-builder": "^26.0.12", "electron-store": "^10.1.0", "moment": "^2.30.1", "node-thermal-printer": "^4.5.0", "qrcode-generator": "^2.0.2", "serialport": "^13.0.0", "sqlite3": "^5.1.7", "uuid": "^11.1.0"}}